// 微信公众号排版样式配置
export interface FormatStyle {
  id: string;
  name: string;
  description: string;
  styles: {
    title: string;
    subtitle: string;
    paragraph: string;
    quote: string;
    list: string;
    image: string;
    divider: string;
    emphasis: string;
    link: string;
  };
}

// 预设的排版样式
export const formatStyles: FormatStyle[] = [
  {
    id: 'classic',
    name: '经典商务',
    description: '适合商务、新闻类文章，简洁专业',
    styles: {
      title: 'font-size: 24px; font-weight: bold; color: #333; text-align: center; margin: 20px 0; line-height: 1.4;',
      subtitle: 'font-size: 18px; font-weight: bold; color: #555; margin: 16px 0 8px 0; line-height: 1.4;',
      paragraph: 'font-size: 16px; color: #333; line-height: 1.8; margin: 12px 0; text-indent: 2em;',
      quote: 'font-size: 15px; color: #666; background: #f5f5f5; padding: 15px; margin: 15px 0; border-left: 4px solid #007acc; font-style: italic; line-height: 1.6;',
      list: 'font-size: 16px; color: #333; line-height: 1.6; margin: 8px 0 8px 20px;',
      image: 'width: 100%; margin: 15px 0; border-radius: 8px;',
      divider: 'border: none; height: 1px; background: #e0e0e0; margin: 25px 0;',
      emphasis: 'font-weight: bold; color: #007acc;',
      link: 'color: #007acc; text-decoration: none;'
    }
  },
  {
    id: 'modern',
    name: '现代时尚',
    description: '适合时尚、生活类文章，活泼现代',
    styles: {
      title: 'font-size: 26px; font-weight: bold; color: #2c3e50; text-align: center; margin: 25px 0; line-height: 1.3; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;',
      subtitle: 'font-size: 20px; font-weight: bold; color: #34495e; margin: 20px 0 10px 0; line-height: 1.3; border-bottom: 2px solid #3498db; padding-bottom: 5px;',
      paragraph: 'font-size: 16px; color: #2c3e50; line-height: 1.8; margin: 15px 0; text-indent: 0;',
      quote: 'font-size: 16px; color: #7f8c8d; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); padding: 20px; margin: 20px 0; border-radius: 10px; border-left: 5px solid #3498db; line-height: 1.7;',
      list: 'font-size: 16px; color: #2c3e50; line-height: 1.7; margin: 10px 0 10px 25px;',
      image: 'width: 100%; margin: 20px 0; border-radius: 15px; box-shadow: 0 8px 32px rgba(0,0,0,0.1);',
      divider: 'border: none; height: 2px; background: linear-gradient(90deg, #3498db, #9b59b6); margin: 30px 0; border-radius: 1px;',
      emphasis: 'font-weight: bold; color: #e74c3c; background: #fff3cd; padding: 2px 6px; border-radius: 4px;',
      link: 'color: #3498db; text-decoration: none; border-bottom: 1px dotted #3498db;'
    }
  },
  {
    id: 'minimal',
    name: '极简清新',
    description: '适合文艺、清新类文章，简约优雅',
    styles: {
      title: 'font-size: 22px; font-weight: 300; color: #444; text-align: center; margin: 30px 0; line-height: 1.5; letter-spacing: 2px;',
      subtitle: 'font-size: 18px; font-weight: 400; color: #666; margin: 25px 0 15px 0; line-height: 1.4; letter-spacing: 1px;',
      paragraph: 'font-size: 15px; color: #555; line-height: 2; margin: 18px 0; text-indent: 2em; letter-spacing: 0.5px;',
      quote: 'font-size: 14px; color: #888; background: #fafafa; padding: 20px; margin: 25px 0; border: none; border-left: 3px solid #ddd; line-height: 1.8; font-style: italic;',
      list: 'font-size: 15px; color: #555; line-height: 1.8; margin: 12px 0 12px 30px;',
      image: 'width: 100%; margin: 25px 0; border-radius: 0;',
      divider: 'border: none; height: 1px; background: #eee; margin: 40px 0;',
      emphasis: 'font-weight: 500; color: #333; background: #f9f9f9; padding: 1px 4px;',
      link: 'color: #666; text-decoration: underline; text-decoration-color: #ccc;'
    }
  },
  {
    id: 'tech',
    name: '科技蓝调',
    description: '适合科技、IT类文章，专业现代',
    styles: {
      title: 'font-size: 24px; font-weight: bold; color: #1a1a1a; text-align: center; margin: 20px 0; line-height: 1.4; background: #f0f8ff; padding: 15px; border-radius: 8px; border: 2px solid #4a90e2;',
      subtitle: 'font-size: 19px; font-weight: bold; color: #2c3e50; margin: 18px 0 10px 0; line-height: 1.4; background: #4a90e2; color: white; padding: 8px 15px; border-radius: 5px;',
      paragraph: 'font-size: 16px; color: #2c3e50; line-height: 1.7; margin: 14px 0; text-indent: 0; background: #f8f9fa; padding: 10px; border-radius: 5px;',
      quote: 'font-size: 15px; color: #34495e; background: #ecf0f1; padding: 18px; margin: 18px 0; border-left: 4px solid #4a90e2; line-height: 1.6; font-family: "Courier New", monospace;',
      list: 'font-size: 16px; color: #2c3e50; line-height: 1.6; margin: 10px 0 10px 25px; background: #f8f9fa; padding: 5px 10px; border-radius: 3px;',
      image: 'width: 100%; margin: 18px 0; border-radius: 10px; border: 2px solid #e3e3e3;',
      divider: 'border: none; height: 2px; background: linear-gradient(90deg, #4a90e2, #50c8ff); margin: 25px 0;',
      emphasis: 'font-weight: bold; color: #4a90e2; background: #e8f4fd; padding: 3px 8px; border-radius: 4px; border: 1px solid #4a90e2;',
      link: 'color: #4a90e2; text-decoration: none; font-weight: 500;'
    }
  },
  {
    id: 'compatible',
    name: '兼容模式',
    description: '高兼容性样式，适合复制到其他编辑器',
    styles: {
      title: 'font-size: 24px; font-weight: bold; color: #333; text-align: center; margin: 20px 0; line-height: 1.4; background: #f0f8ff; padding: 15px; border: 3px solid #3498db;',
      subtitle: 'font-size: 18px; font-weight: bold; color: #555; margin: 16px 0 8px 0; line-height: 1.4; border-bottom: 2px solid #3498db; padding-bottom: 5px;',
      paragraph: 'font-size: 16px; color: #333; line-height: 1.8; margin: 12px 0; text-indent: 0;',
      quote: 'font-size: 15px; color: #666; background: #f5f5f5; padding: 15px; margin: 15px 0; border: 2px solid #3498db; line-height: 1.6;',
      list: 'font-size: 16px; color: #333; line-height: 1.6; margin: 8px 0 8px 20px;',
      image: 'width: 100%; margin: 15px 0; border: 2px solid #3498db;',
      divider: 'border: none; height: 2px; background: #3498db; margin: 20px 0;',
      emphasis: 'font-weight: bold; color: #3498db; background: #f0f8ff; padding: 2px 6px; border: 1px solid #3498db;',
      link: 'color: #3498db; text-decoration: underline; font-weight: 500;'
    }
  }
];

// 内容类型枚举
export enum ContentType {
  TITLE = 'title',
  SUBTITLE = 'subtitle',
  PARAGRAPH = 'paragraph',
  QUOTE = 'quote',
  LIST = 'list',
  IMAGE = 'image',
  DIVIDER = 'divider',
  EMPHASIS = 'emphasis',
  LINK = 'link'
}

// 解析内容并识别类型
export function parseContent(htmlContent: string): Array<{type: ContentType, content: string, level?: number}> {
  // 检查是否在浏览器环境
  if (typeof window !== 'undefined') {
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');
    const elements = doc.body.children;
    return parseElements(elements);
  } else {
    // 服务器端使用jsdom
    const { JSDOM } = require('jsdom');
    const dom = new JSDOM(htmlContent);
    const elements = dom.window.document.body.children;
    return parseElements(elements);
  }
}

// 解析DOM元素的通用函数
function parseElements(elements: HTMLCollection): Array<{type: ContentType, content: string, level?: number}> {
  const result: Array<{type: ContentType, content: string, level?: number}> = [];

  for (let i = 0; i < elements.length; i++) {
    const element = elements[i] as Element;
    const tagName = element.tagName.toLowerCase();
    const textContent = element.textContent?.trim() || '';

    if (!textContent && tagName !== 'hr' && tagName !== 'img') continue;

    switch (tagName) {
      case 'h1':
        result.push({ type: ContentType.TITLE, content: textContent });
        break;
      case 'h2':
      case 'h3':
      case 'h4':
      case 'h5':
      case 'h6':
        result.push({ type: ContentType.SUBTITLE, content: textContent, level: parseInt(tagName[1]) });
        break;
      case 'p':
        if (textContent.length > 0) {
          result.push({ type: ContentType.PARAGRAPH, content: textContent });
        }
        break;
      case 'blockquote':
        result.push({ type: ContentType.QUOTE, content: textContent });
        break;
      case 'ul':
      case 'ol':
        const listItems = element.querySelectorAll('li');
        listItems.forEach(li => {
          const itemText = li.textContent?.trim();
          if (itemText) {
            result.push({ type: ContentType.LIST, content: itemText });
          }
        });
        break;
      case 'img':
        const src = element.getAttribute('src');
        if (src) {
          result.push({ type: ContentType.IMAGE, content: src });
        }
        break;
      case 'hr':
        result.push({ type: ContentType.DIVIDER, content: '' });
        break;
      default:
        // 处理其他包含文本的元素
        if (textContent.length > 0) {
          result.push({ type: ContentType.PARAGRAPH, content: textContent });
        }
        break;
    }
  }

  return result;
}

// 应用格式化样式
export function applyFormatting(
  parsedContent: Array<{type: ContentType, content: string, level?: number}>,
  styleId: string
): string {
  const style = formatStyles.find(s => s.id === styleId);
  if (!style) {
    throw new Error(`Style ${styleId} not found`);
  }

  let formattedHtml = '';

  parsedContent.forEach(item => {
    switch (item.type) {
      case ContentType.TITLE:
        formattedHtml += `<h1 style="${style.styles.title}">${item.content}</h1>\n`;
        break;
      case ContentType.SUBTITLE:
        formattedHtml += `<h${item.level || 2} style="${style.styles.subtitle}">${item.content}</h${item.level || 2}>\n`;
        break;
      case ContentType.PARAGRAPH:
        formattedHtml += `<p style="${style.styles.paragraph}">${item.content}</p>\n`;
        break;
      case ContentType.QUOTE:
        formattedHtml += `<blockquote style="${style.styles.quote}">${item.content}</blockquote>\n`;
        break;
      case ContentType.LIST:
        formattedHtml += `<li style="${style.styles.list}">${item.content}</li>\n`;
        break;
      case ContentType.IMAGE:
        formattedHtml += `<img src="${item.content}" style="${style.styles.image}" alt="文章图片" />\n`;
        break;
      case ContentType.DIVIDER:
        formattedHtml += `<hr style="${style.styles.divider}" />\n`;
        break;
      default:
        formattedHtml += `<p style="${style.styles.paragraph}">${item.content}</p>\n`;
        break;
    }
  });

  return formattedHtml;
}

// 一键自动排版
export function autoFormat(htmlContent: string, styleId: string = 'classic'): string {
  const parsedContent = parseContent(htmlContent);
  return applyFormatting(parsedContent, styleId);
}
