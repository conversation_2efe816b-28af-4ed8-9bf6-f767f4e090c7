{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/wx_community/wx-article-formatter/src/components/RichTextEditor.tsx"], "sourcesContent": ["'use client';\n\nimport { useEditor, EditorContent } from '@tiptap/react';\nimport StarterKit from '@tiptap/starter-kit';\nimport Image from '@tiptap/extension-image';\nimport Link from '@tiptap/extension-link';\nimport { useCallback, useEffect, useState } from 'react';\n\ninterface RichTextEditorProps {\n  content: string;\n  onChange: (content: string) => void;\n  placeholder?: string;\n}\n\nconst RichTextEditor: React.FC<RichTextEditorProps> = ({\n  content,\n  onChange,\n  placeholder = '请输入内容...'\n}) => {\n  const [isMounted, setIsMounted] = useState(false);\n\n  useEffect(() => {\n    setIsMounted(true);\n  }, []);\n\n  const editor = useEditor({\n    extensions: [\n      StarterKit,\n      Image.configure({\n        HTMLAttributes: {\n          class: 'max-w-full h-auto rounded-lg',\n          style: 'max-width: 100%; height: auto; margin: 15px auto; border-radius: 8px; display: block; box-shadow: 0 4px 12px rgba(0,0,0,0.1);',\n        },\n        allowBase64: true,\n        inline: false,\n      }),\n      Link.configure({\n        openOnClick: false,\n        HTMLAttributes: {\n          class: 'text-blue-600 underline',\n        },\n      }),\n    ],\n    content,\n    onUpdate: ({ editor }) => {\n      onChange(editor.getHTML());\n    },\n    editorProps: {\n      attributes: {\n        class: 'prose prose-lg max-w-none focus:outline-none min-h-[400px] p-6 leading-relaxed',\n        style: 'font-family: \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", sans-serif; line-height: 1.8; color: #333;',\n      },\n    },\n    immediatelyRender: false,\n  });\n\n  // 当content prop变化时，更新编辑器内容\n  useEffect(() => {\n    if (editor && content !== editor.getHTML()) {\n      editor.commands.setContent(content);\n    }\n  }, [content, editor]);\n\n  const addImage = useCallback(() => {\n    const url = window.prompt('请输入图片URL:');\n    if (url && editor) {\n      editor.chain().focus().setImage({ src: url }).run();\n    }\n  }, [editor]);\n\n  const uploadImage = useCallback(() => {\n    const input = document.createElement('input');\n    input.type = 'file';\n    input.accept = 'image/*';\n    input.onchange = async (e) => {\n      const file = (e.target as HTMLInputElement).files?.[0];\n      if (file && editor) {\n        try {\n          const formData = new FormData();\n          formData.append('image', file);\n\n          const response = await fetch('/api/upload-image', {\n            method: 'POST',\n            body: formData,\n          });\n\n          if (response.ok) {\n            const result = await response.json();\n            editor.chain().focus().setImage({ src: result.url }).run();\n          } else {\n            const error = await response.json();\n            alert(`上传失败: ${error.error}`);\n          }\n        } catch (error) {\n          console.error('图片上传失败:', error);\n          alert('图片上传失败，请重试');\n        }\n      }\n    };\n    input.click();\n  }, [editor]);\n\n  const setLink = useCallback(() => {\n    const previousUrl = editor?.getAttributes('link').href;\n    const url = window.prompt('请输入链接URL:', previousUrl);\n\n    if (url === null) {\n      return;\n    }\n\n    if (url === '') {\n      editor?.chain().focus().extendMarkRange('link').unsetLink().run();\n      return;\n    }\n\n    editor?.chain().focus().extendMarkRange('link').setLink({ href: url }).run();\n  }, [editor]);\n\n  if (!isMounted) {\n    return (\n      <div className=\"border border-gray-300 rounded-lg overflow-hidden\">\n        <div className=\"border-b border-gray-300 p-2 bg-gray-50\">\n          <div className=\"h-8 bg-gray-200 rounded animate-pulse\"></div>\n        </div>\n        <div className=\"bg-white p-6 min-h-[400px] flex items-center justify-center\">\n          <div className=\"text-gray-500\">加载编辑器中...</div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!editor) {\n    return (\n      <div className=\"border border-gray-300 rounded-lg overflow-hidden\">\n        <div className=\"bg-white p-6 min-h-[400px] flex items-center justify-center\">\n          <div className=\"text-gray-500\">编辑器初始化中...</div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"border border-gray-300 rounded-lg overflow-hidden\">\n      {/* 工具栏 */}\n      <div className=\"border-b border-gray-300 p-2 bg-gray-50 flex flex-wrap gap-1\">\n        <button\n          onClick={() => editor.chain().focus().toggleBold().run()}\n          disabled={!editor.can().chain().focus().toggleBold().run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('bold')\n              ? 'bg-blue-600 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          } border border-gray-300`}\n        >\n          粗体\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleItalic().run()}\n          disabled={!editor.can().chain().focus().toggleItalic().run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('italic')\n              ? 'bg-blue-600 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          } border border-gray-300`}\n        >\n          斜体\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleStrike().run()}\n          disabled={!editor.can().chain().focus().toggleStrike().run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('strike')\n              ? 'bg-blue-600 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          } border border-gray-300`}\n        >\n          删除线\n        </button>\n        <div className=\"w-px h-6 bg-gray-300 mx-1\"></div>\n        <button\n          onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('heading', { level: 1 })\n              ? 'bg-blue-600 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          } border border-gray-300`}\n        >\n          H1\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('heading', { level: 2 })\n              ? 'bg-blue-600 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          } border border-gray-300`}\n        >\n          H2\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('heading', { level: 3 })\n              ? 'bg-blue-600 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          } border border-gray-300`}\n        >\n          H3\n        </button>\n        <div className=\"w-px h-6 bg-gray-300 mx-1\"></div>\n        <button\n          onClick={() => editor.chain().focus().toggleBulletList().run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('bulletList')\n              ? 'bg-blue-600 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          } border border-gray-300`}\n        >\n          无序列表\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleOrderedList().run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('orderedList')\n              ? 'bg-blue-600 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          } border border-gray-300`}\n        >\n          有序列表\n        </button>\n        <div className=\"w-px h-6 bg-gray-300 mx-1\"></div>\n        <button\n          onClick={setLink}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('link')\n              ? 'bg-blue-600 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          } border border-gray-300`}\n        >\n          链接\n        </button>\n        <button\n          onClick={addImage}\n          className=\"px-3 py-1 rounded text-sm font-medium bg-white text-gray-700 hover:bg-gray-100 border border-gray-300\"\n        >\n          插入链接图片\n        </button>\n        <button\n          onClick={uploadImage}\n          className=\"px-3 py-1 rounded text-sm font-medium bg-white text-gray-700 hover:bg-gray-100 border border-gray-300\"\n        >\n          上传图片\n        </button>\n        <div className=\"w-px h-6 bg-gray-300 mx-1\"></div>\n        <button\n          onClick={() => editor.chain().focus().toggleBlockquote().run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('blockquote')\n              ? 'bg-blue-600 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          } border border-gray-300`}\n        >\n          引用\n        </button>\n        <button\n          onClick={() => editor.chain().focus().setHorizontalRule().run()}\n          className=\"px-3 py-1 rounded text-sm font-medium bg-white text-gray-700 hover:bg-gray-100 border border-gray-300\"\n        >\n          分割线\n        </button>\n        <div className=\"w-px h-6 bg-gray-300 mx-1\"></div>\n        <button\n          onClick={() => editor.chain().focus().undo().run()}\n          disabled={!editor.can().chain().focus().undo().run()}\n          className=\"px-3 py-1 rounded text-sm font-medium bg-white text-gray-700 hover:bg-gray-100 border border-gray-300 disabled:opacity-50\"\n        >\n          撤销\n        </button>\n        <button\n          onClick={() => editor.chain().focus().redo().run()}\n          disabled={!editor.can().chain().focus().redo().run()}\n          className=\"px-3 py-1 rounded text-sm font-medium bg-white text-gray-700 hover:bg-gray-100 border border-gray-300 disabled:opacity-50\"\n        >\n          重做\n        </button>\n      </div>\n\n      {/* 编辑器内容区域 */}\n      <div className=\"bg-white\">\n        <EditorContent editor={editor} />\n      </div>\n    </div>\n  );\n};\n\nexport default RichTextEditor;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAcA,MAAM,iBAAgD;QAAC,EACrD,OAAO,EACP,QAAQ,EACR,cAAc,UAAU,EACzB;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,aAAa;QACf;mCAAG,EAAE;IAEL,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD,EAAE;QACvB,YAAY;YACV,8JAAA,CAAA,UAAU;YACV,kKAAA,CAAA,UAAK,CAAC,SAAS,CAAC;gBACd,gBAAgB;oBACd,OAAO;oBACP,OAAO;gBACT;gBACA,aAAa;gBACb,QAAQ;YACV;YACA,iKAAA,CAAA,UAAI,CAAC,SAAS,CAAC;gBACb,aAAa;gBACb,gBAAgB;oBACd,OAAO;gBACT;YACF;SACD;QACD;QACA,QAAQ;gDAAE;oBAAC,EAAE,MAAM,EAAE;gBACnB,SAAS,OAAO,OAAO;YACzB;;QACA,aAAa;YACX,YAAY;gBACV,OAAO;gBACP,OAAO;YACT;QACF;QACA,mBAAmB;IACrB;IAEA,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,UAAU,YAAY,OAAO,OAAO,IAAI;gBAC1C,OAAO,QAAQ,CAAC,UAAU,CAAC;YAC7B;QACF;mCAAG;QAAC;QAAS;KAAO;IAEpB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE;YAC3B,MAAM,MAAM,OAAO,MAAM,CAAC;YAC1B,IAAI,OAAO,QAAQ;gBACjB,OAAO,KAAK,GAAG,KAAK,GAAG,QAAQ,CAAC;oBAAE,KAAK;gBAAI,GAAG,GAAG;YACnD;QACF;+CAAG;QAAC;KAAO;IAEX,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YAC9B,MAAM,QAAQ,SAAS,aAAa,CAAC;YACrC,MAAM,IAAI,GAAG;YACb,MAAM,MAAM,GAAG;YACf,MAAM,QAAQ;2DAAG,OAAO;wBACT;oBAAb,MAAM,QAAO,SAAA,AAAC,EAAE,MAAM,CAAsB,KAAK,cAApC,6BAAA,MAAsC,CAAC,EAAE;oBACtD,IAAI,QAAQ,QAAQ;wBAClB,IAAI;4BACF,MAAM,WAAW,IAAI;4BACrB,SAAS,MAAM,CAAC,SAAS;4BAEzB,MAAM,WAAW,MAAM,MAAM,qBAAqB;gCAChD,QAAQ;gCACR,MAAM;4BACR;4BAEA,IAAI,SAAS,EAAE,EAAE;gCACf,MAAM,SAAS,MAAM,SAAS,IAAI;gCAClC,OAAO,KAAK,GAAG,KAAK,GAAG,QAAQ,CAAC;oCAAE,KAAK,OAAO,GAAG;gCAAC,GAAG,GAAG;4BAC1D,OAAO;gCACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gCACjC,MAAM,AAAC,SAAoB,OAAZ,MAAM,KAAK;4BAC5B;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,WAAW;4BACzB,MAAM;wBACR;oBACF;gBACF;;YACA,MAAM,KAAK;QACb;kDAAG;QAAC;KAAO;IAEX,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE;YAC1B,MAAM,cAAc,mBAAA,6BAAA,OAAQ,aAAa,CAAC,QAAQ,IAAI;YACtD,MAAM,MAAM,OAAO,MAAM,CAAC,aAAa;YAEvC,IAAI,QAAQ,MAAM;gBAChB;YACF;YAEA,IAAI,QAAQ,IAAI;gBACd,mBAAA,6BAAA,OAAQ,KAAK,GAAG,KAAK,GAAG,eAAe,CAAC,QAAQ,SAAS,GAAG,GAAG;gBAC/D;YACF;YAEA,mBAAA,6BAAA,OAAQ,KAAK,GAAG,KAAK,GAAG,eAAe,CAAC,QAAQ,OAAO,CAAC;gBAAE,MAAM;YAAI,GAAG,GAAG;QAC5E;8CAAG;QAAC;KAAO;IAEX,IAAI,CAAC,WAAW;QACd,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;;8BAEjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIvC;IAEA,IAAI,CAAC,QAAQ;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BAAgB;;;;;;;;;;;;;;;;IAIvC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,GAAG;wBACtD,UAAU,CAAC,OAAO,GAAG,GAAG,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,GAAG;wBACxD,WAAW,AAAC,yCAIX,OAHC,OAAO,QAAQ,CAAC,UACZ,2BACA,4CACL;kCACF;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,YAAY,GAAG,GAAG;wBACxD,UAAU,CAAC,OAAO,GAAG,GAAG,KAAK,GAAG,KAAK,GAAG,YAAY,GAAG,GAAG;wBAC1D,WAAW,AAAC,yCAIX,OAHC,OAAO,QAAQ,CAAC,YACZ,2BACA,4CACL;kCACF;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,YAAY,GAAG,GAAG;wBACxD,UAAU,CAAC,OAAO,GAAG,GAAG,KAAK,GAAG,KAAK,GAAG,YAAY,GAAG,GAAG;wBAC1D,WAAW,AAAC,yCAIX,OAHC,OAAO,QAAQ,CAAC,YACZ,2BACA,4CACL;kCACF;;;;;;kCAGD,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,aAAa,CAAC;gCAAE,OAAO;4BAAE,GAAG,GAAG;wBACrE,WAAW,AAAC,yCAIX,OAHC,OAAO,QAAQ,CAAC,WAAW;4BAAE,OAAO;wBAAE,KAClC,2BACA,4CACL;kCACF;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,aAAa,CAAC;gCAAE,OAAO;4BAAE,GAAG,GAAG;wBACrE,WAAW,AAAC,yCAIX,OAHC,OAAO,QAAQ,CAAC,WAAW;4BAAE,OAAO;wBAAE,KAClC,2BACA,4CACL;kCACF;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,aAAa,CAAC;gCAAE,OAAO;4BAAE,GAAG,GAAG;wBACrE,WAAW,AAAC,yCAIX,OAHC,OAAO,QAAQ,CAAC,WAAW;4BAAE,OAAO;wBAAE,KAClC,2BACA,4CACL;kCACF;;;;;;kCAGD,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,gBAAgB,GAAG,GAAG;wBAC5D,WAAW,AAAC,yCAIX,OAHC,OAAO,QAAQ,CAAC,gBACZ,2BACA,4CACL;kCACF;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,iBAAiB,GAAG,GAAG;wBAC7D,WAAW,AAAC,yCAIX,OAHC,OAAO,QAAQ,CAAC,iBACZ,2BACA,4CACL;kCACF;;;;;;kCAGD,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBACC,SAAS;wBACT,WAAW,AAAC,yCAIX,OAHC,OAAO,QAAQ,CAAC,UACZ,2BACA,4CACL;kCACF;;;;;;kCAGD,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAGD,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAGD,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,gBAAgB,GAAG,GAAG;wBAC5D,WAAW,AAAC,yCAIX,OAHC,OAAO,QAAQ,CAAC,gBACZ,2BACA,4CACL;kCACF;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,iBAAiB,GAAG,GAAG;wBAC7D,WAAU;kCACX;;;;;;kCAGD,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG;wBAChD,UAAU,CAAC,OAAO,GAAG,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG;wBAClD,WAAU;kCACX;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG;wBAChD,UAAU,CAAC,OAAO,GAAG,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG;wBAClD,WAAU;kCACX;;;;;;;;;;;;0BAMH,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qKAAA,CAAA,gBAAa;oBAAC,QAAQ;;;;;;;;;;;;;;;;;AAI/B;GAvRM;;QAWW,qKAAA,CAAA,YAAS;;;KAXpB;uCAyRS", "debugId": null}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/wx_community/wx-article-formatter/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport RichTextEditor from '@/components/RichTextEditor';\n\nexport default function Home() {\n  const [url, setUrl] = useState('');\n  const [content, setContent] = useState('');\n  const [richTextContent, setRichTextContent] = useState('');\n  const [extractedData, setExtractedData] = useState<any>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState<'url' | 'paste' | 'editor'>('url');\n  const [selectedStyle, setSelectedStyle] = useState('classic');\n  const [formattedContent, setFormattedContent] = useState('');\n  const [availableStyles, setAvailableStyles] = useState<any[]>([]);\n  const [originalityResult, setOriginalityResult] = useState<any>(null);\n  const [isCheckingOriginality, setIsCheckingOriginality] = useState(false);\n  const [isPreviewMode, setIsPreviewMode] = useState(false);\n\n  // 获取可用的排版样式\n  const fetchAvailableStyles = async () => {\n    try {\n      const response = await fetch('/api/format');\n      if (response.ok) {\n        const data = await response.json();\n        setAvailableStyles(data.styles);\n      }\n    } catch (error) {\n      console.error('获取样式失败:', error);\n    }\n  };\n\n  // 组件加载时获取样式\n  React.useEffect(() => {\n    fetchAvailableStyles();\n  }, []);\n\n  // 处理图片URL，使用代理解决跨域问题\n  const processImageUrl = (url: string) => {\n    if (!url) return '';\n\n    // 如果是相对路径或协议相对路径，跳过\n    if (url.startsWith('/') && !url.startsWith('//')) {\n      return url;\n    }\n\n    // 如果是协议相对路径，添加https\n    if (url.startsWith('//')) {\n      url = 'https:' + url;\n    }\n\n    // 使用代理URL来避免跨域问题\n    return `/api/image-proxy?url=${encodeURIComponent(url)}`;\n  };\n\n  // 生成兼容性更好的富文本内容（用于复制到其他编辑器）\n  const convertToCompatibleRichText = (data: any) => {\n    let richContent = `\n      <table style=\"width: 100%; max-width: 800px; margin: 0 auto; font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif; line-height: 1.8; color: #333; border-collapse: collapse;\">\n        <tr>\n          <td style=\"padding: 0;\">\n            <!-- 标题区域 -->\n            <table style=\"width: 100%; margin-bottom: 30px; border-collapse: collapse;\">\n              <tr>\n                <td style=\"text-align: center;\">\n                  <h1 style=\"\n                    font-size: 28px;\n                    font-weight: bold;\n                    margin: 20px 0 10px 0;\n                    padding: 15px 25px;\n                    background: #f0f8ff;\n                    color: #2c3e50;\n                    border: 3px solid #3498db;\n                    display: inline-block;\n                  \">${data.title || '文章标题'}</h1>`;\n\n    // 副标题\n    if (data.subtitle) {\n      richContent += `\n                  <h2 style=\"\n                    font-size: 20px;\n                    font-weight: bold;\n                    color: #34495e;\n                    margin: 15px 0 5px 0;\n                    text-align: center;\n                  \">${data.subtitle}</h2>`;\n    }\n\n    // 作者信息\n    if (data.author) {\n      richContent += `\n                  <p style=\"\n                    text-align: center;\n                    font-size: 16px;\n                    color: #7f8c8d;\n                    margin: 10px 0 20px 0;\n                  \">作者：${data.author}</p>`;\n    }\n\n    richContent += `\n                </td>\n              </tr>\n            </table>`;\n\n    // 摘要\n    if (data.summary && data.hasSummary) {\n      richContent += `\n            <table style=\"width: 100%; background: #fff3cd; margin: 25px 0; border: 2px solid #ffc107; border-collapse: collapse;\">\n              <tr>\n                <td style=\"padding: 20px;\">\n                  <h3 style=\"\n                    font-size: 18px;\n                    font-weight: bold;\n                    color: #856404;\n                    margin: 0 0 10px 0;\n                    border-bottom: 2px solid #ffc107;\n                    padding-bottom: 5px;\n                    display: inline-block;\n                  \">摘要</h3>\n                  <p style=\"\n                    margin: 0;\n                    font-size: 15px;\n                    color: #856404;\n                    line-height: 1.7;\n                    text-indent: 0;\n                  \">${data.summary}</p>\n                </td>\n              </tr>\n            </table>`;\n    }\n\n    // 处理正文内容\n    let contentText = '';\n    if (data.content && data.content.includes('<')) {\n      const tempDiv = document.createElement('div');\n      tempDiv.innerHTML = data.content;\n      const paragraphs = Array.from(tempDiv.querySelectorAll('p')).map(p => p.textContent?.trim()).filter(Boolean);\n      contentText = paragraphs.join('\\n\\n');\n    } else {\n      contentText = data.content || '';\n    }\n\n    // 检查并移除已存在的版权声明，避免重复\n    contentText = contentText.replace(/本网站作品著作权归作者本人所有，凡发表在网站的文章，未经作者本人授权，不得转载。/g, '').trim();\n\n    // 正文内容区域\n    richContent += `\n            <table style=\"width: 100%; background: #f8f9fa; margin: 25px 0; border: 2px solid #3498db; border-collapse: collapse;\">\n              <tr>\n                <td style=\"padding: 25px;\">`;\n\n    // 处理正文段落和图片\n    const paragraphs = contentText.split('\\n').filter(p => p.trim());\n    const images = data.images || [];\n\n    if (images.length > 0) {\n      const imagePositions = [];\n      const interval = Math.max(1, Math.floor(paragraphs.length / images.length));\n      for (let i = 0; i < images.length; i++) {\n        imagePositions.push(Math.min(paragraphs.length, (i + 1) * interval));\n      }\n\n      let imageIndex = 0;\n      for (let i = 0; i <= paragraphs.length; i++) {\n        if (imagePositions.includes(i) && imageIndex < images.length) {\n          const proxiedImg = processImageUrl(images[imageIndex]);\n          richContent += `\n                  <table style=\"width: 100%; margin: 25px 0; border-collapse: collapse;\">\n                    <tr>\n                      <td style=\"text-align: center;\">\n                        <img src=\"${proxiedImg}\" alt=\"图片 ${imageIndex + 1}\" style=\"\n                          max-width: 100%;\n                          height: auto;\n                          border: 2px solid #3498db;\n                        \" />\n                      </td>\n                    </tr>\n                  </table>`;\n          imageIndex++;\n        }\n\n        if (i < paragraphs.length) {\n          richContent += `\n                  <p style=\"\n                    margin: 15px 0;\n                    font-size: 16px;\n                    line-height: 1.8;\n                    color: #333;\n                    text-indent: 0;\n                  \">${paragraphs[i].trim()}</p>`;\n        }\n      }\n\n      while (imageIndex < images.length) {\n        const proxiedImg = processImageUrl(images[imageIndex]);\n        richContent += `\n                  <table style=\"width: 100%; margin: 25px 0; border-collapse: collapse;\">\n                    <tr>\n                      <td style=\"text-align: center;\">\n                        <img src=\"${proxiedImg}\" alt=\"图片 ${imageIndex + 1}\" style=\"\n                          max-width: 100%;\n                          height: auto;\n                          border: 2px solid #3498db;\n                        \" />\n                      </td>\n                    </tr>\n                  </table>`;\n        imageIndex++;\n      }\n    } else {\n      paragraphs.forEach(paragraph => {\n        richContent += `\n                  <p style=\"\n                    margin: 15px 0;\n                    font-size: 16px;\n                    line-height: 1.8;\n                    color: #333;\n                    text-indent: 0;\n                  \">${paragraph.trim()}</p>`;\n      });\n    }\n\n    // 版权声明\n    richContent += `\n                  <p style=\"\n                    margin: 30px 0 15px 0;\n                    font-size: 14px;\n                    color: #999;\n                    text-align: left;\n                    text-indent: 0;\n                    border-top: 1px solid #eee;\n                    padding-top: 15px;\n                  \">本网站作品著作权归作者本人所有，凡发表在网站的文章，未经作者本人授权，不得转载。</p>\n                </td>\n              </tr>\n            </table>`;\n\n    // 编者按和作者简介区域\n    if (data.editorNote || data.authorBio) {\n      richContent += `\n            <table style=\"width: 100%; background: #fff; margin: 25px 0; border: 2px solid #e9ecef; border-collapse: collapse;\">\n              <tr>\n                <td style=\"padding: 25px;\">`;\n\n      if (data.editorNote) {\n        let cleanEditorNote = data.editorNote;\n        cleanEditorNote = cleanEditorNote.replace(/^【?编者按】?[：:]?\\s*/i, '');\n        cleanEditorNote = cleanEditorNote.replace(/^编者按[：:]?\\s*/i, '');\n\n        richContent += `\n                  <h3 style=\"\n                    font-size: 18px;\n                    font-weight: bold;\n                    color: #333;\n                    margin: 0 0 10px 0;\n                    border-bottom: 2px solid #3498db;\n                    padding-bottom: 5px;\n                    display: inline-block;\n                  \">编者按</h3>\n                  <p style=\"\n                    margin: 0 0 20px 0;\n                    font-size: 15px;\n                    color: #555;\n                    line-height: 1.7;\n                    text-indent: 0;\n                  \">${cleanEditorNote}</p>`;\n      }\n\n      if (data.authorBio) {\n        let cleanAuthorBio = data.authorBio;\n        cleanAuthorBio = cleanAuthorBio.replace(/^作者[：:]?\\s*/i, '');\n\n        richContent += `\n                  <h3 style=\"\n                    font-size: 18px;\n                    font-weight: bold;\n                    color: #333;\n                    margin: 0 0 15px 0;\n                    border-bottom: 2px solid #3498db;\n                    padding-bottom: 5px;\n                    display: inline-block;\n                  \">作者简介</h3>\n                  <table style=\"width: 100%; border-collapse: collapse;\">\n                    <tr>`;\n\n        if (data.authorPhoto) {\n          const proxiedPhoto = processImageUrl(data.authorPhoto);\n          richContent += `\n                      <td style=\"width: 80px; vertical-align: top; padding-right: 15px;\">\n                        <img src=\"${proxiedPhoto}\" alt=\"${data.author}\" style=\"\n                          width: 80px;\n                          height: 80px;\n                          border: 3px solid #3498db;\n                        \" />\n                      </td>`;\n        }\n\n        richContent += `\n                      <td style=\"vertical-align: top;\">\n                        <p style=\"\n                          margin: 0 0 5px 0;\n                          font-size: 16px;\n                          font-weight: bold;\n                          color: #333;\n                        \">${data.author}</p>\n                        <p style=\"\n                          margin: 0;\n                          font-size: 15px;\n                          color: #555;\n                          line-height: 1.7;\n                          text-indent: 0;\n                        \">${cleanAuthorBio}</p>\n                      </td>\n                    </tr>\n                  </table>`;\n      }\n\n      richContent += `\n                </td>\n              </tr>\n            </table>`;\n    }\n\n    richContent += `\n          </td>\n        </tr>\n      </table>`;\n\n    return richContent;\n  };\n\n  // 生成随机的文章样式\n  const generateArticleStyle = () => {\n    const styles = [\n      {\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        textColor: 'white',\n        borderColor: '#667eea'\n      },\n      {\n        background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n        textColor: 'white',\n        borderColor: '#f093fb'\n      },\n      {\n        background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',\n        textColor: 'white',\n        borderColor: '#4facfe'\n      },\n      {\n        background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',\n        textColor: 'white',\n        borderColor: '#43e97b'\n      },\n      {\n        background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',\n        textColor: 'white',\n        borderColor: '#fa709a'\n      }\n    ];\n    return styles[Math.floor(Math.random() * styles.length)];\n  };\n\n  // 将提取的内容转换为富文本格式\n  const convertToRichText = (data: any) => {\n    const style = generateArticleStyle();\n\n    let richContent = `\n      <div style=\"max-width: 800px; margin: 0 auto; font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif; line-height: 1.8; color: #333;\">\n        <!-- 标题区域 -->\n        <div style=\"text-align: center; margin-bottom: 30px;\">\n          <h1 style=\"\n            font-size: 28px;\n            font-weight: bold;\n            margin: 20px 0 10px 0;\n            padding: 15px 25px;\n            background: ${style.background};\n            color: ${style.textColor};\n            border: 3px solid ${style.borderColor};\n            display: inline-block;\n          \">${data.title || '文章标题'}</h1>`;\n\n    if (data.subtitle) {\n      richContent += `\n          <h2 style=\"\n            font-size: 20px;\n            font-weight: 500;\n            margin: 15px 0 10px 0;\n            color: #666;\n          \">${data.subtitle}</h2>`;\n    }\n\n    richContent += `\n          <p style=\"\n            font-size: 16px;\n            color: #888;\n            margin: 10px 0 0 0;\n            font-weight: 500;\n          \">作者：${data.author || '未知作者'}</p>\n        </div>`;\n\n    // 摘要 - 只有当真正存在article-abs且有摘要内容时才显示\n    if (data.hasSummary && data.summary && data.summary.trim()) {\n      // 清理摘要内容，去掉重复的\"摘要：\"\n      let cleanSummary = data.summary.trim();\n      cleanSummary = cleanSummary.replace(/^摘要[：:]?\\s*/i, '');\n\n      // 如果清理后还有内容，才显示摘要区域\n      if (cleanSummary.trim()) {\n        richContent += `\n        <div style=\"\n          background: #f8f9fa;\n          padding: 20px;\n          margin: 25px 0;\n          border-left: 4px solid ${style.borderColor};\n          border-radius: 0 8px 8px 0;\n          box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n        \">\n          <p style=\"\n            margin: 0;\n            font-size: 16px;\n            color: #555;\n            font-style: italic;\n            text-indent: 0;\n          \"><strong>摘要：</strong>${cleanSummary}</p>\n        </div>`;\n      }\n    }\n\n    // 正文内容区域开始\n    richContent += `\n        <div style=\"\n          background: #f8f9fa;\n          padding: 25px;\n          margin: 25px 0;\n          border: 2px solid ${style.borderColor};\n        \">`;\n\n    // 处理正文内容\n    let contentText = '';\n    if (data.content && data.content.includes('<')) {\n      // 如果是HTML格式，清理并提取文本\n      const tempDiv = document.createElement('div');\n      tempDiv.innerHTML = data.content;\n      const paragraphs = Array.from(tempDiv.querySelectorAll('p')).map(p => p.textContent?.trim()).filter(Boolean);\n      contentText = paragraphs.join('\\n\\n');\n    } else {\n      contentText = data.content || '';\n    }\n\n    // 检查并移除已存在的版权声明，避免重复\n    contentText = contentText.replace(/本网站作品著作权归作者本人所有，凡发表在网站的文章，未经作者本人授权，不得转载。/g, '').trim();\n\n    // 处理正文段落和图片的均匀分布\n    const paragraphs = contentText.split('\\n').filter(p => p.trim());\n    const images = data.images || [];\n\n    if (images.length > 0 && paragraphs.length > 0) {\n      // 计算图片插入位置，让图片均匀分布在段落之间\n      const totalSlots = paragraphs.length + 1; // 段落前后都可以插入图片\n      const imagePositions: number[] = [];\n\n      if (images.length >= totalSlots) {\n        // 图片数量多于或等于可插入位置，每个位置都插入\n        for (let i = 0; i < totalSlots; i++) {\n          imagePositions.push(i);\n        }\n      } else {\n        // 图片数量少于可插入位置，均匀分布\n        const step = totalSlots / images.length;\n        for (let i = 0; i < images.length; i++) {\n          const position = Math.floor(i * step);\n          imagePositions.push(position);\n        }\n      }\n\n      // 构建内容，交替插入段落和图片\n      let imageIndex = 0;\n      for (let i = 0; i <= paragraphs.length; i++) {\n        // 检查是否需要在这个位置插入图片\n        if (imagePositions.includes(i) && imageIndex < images.length) {\n          const proxiedImg = processImageUrl(images[imageIndex]);\n          richContent += `\n          <div style=\"text-align: center; margin: 25px 0;\">\n            <img src=\"${proxiedImg}\" alt=\"图片 ${imageIndex + 1}\" style=\"\n              max-width: 100%;\n              height: auto;\n              border: 2px solid ${style.borderColor};\n            \" />\n          </div>`;\n          imageIndex++;\n        }\n\n        // 插入段落（如果不是最后一个位置）\n        if (i < paragraphs.length) {\n          richContent += `\n          <p style=\"\n            margin: 15px 0;\n            font-size: 16px;\n            line-height: 1.8;\n            color: #333;\n            text-indent: 0;\n          \">${paragraphs[i].trim()}</p>`;\n        }\n      }\n\n      // 如果还有剩余图片，添加到最后\n      while (imageIndex < images.length) {\n        const proxiedImg = processImageUrl(images[imageIndex]);\n        richContent += `\n          <div style=\"text-align: center; margin: 25px 0;\">\n            <img src=\"${proxiedImg}\" alt=\"图片 ${imageIndex + 1}\" style=\"\n              max-width: 100%;\n              height: auto;\n              border: 2px solid ${style.borderColor};\n            \" />\n          </div>`;\n        imageIndex++;\n      }\n    } else {\n      // 没有图片时，只添加段落\n      paragraphs.forEach(paragraph => {\n        richContent += `\n          <p style=\"\n            margin: 15px 0;\n            font-size: 16px;\n            line-height: 1.8;\n            color: #333;\n            text-indent: 0;\n          \">${paragraph.trim()}</p>`;\n      });\n    }\n\n    // 版权声明\n    richContent += `\n          <p style=\"\n            margin: 30px 0 15px 0;\n            font-size: 14px;\n            color: #999;\n            text-align: left;\n            text-indent: 0;\n            border-top: 1px solid #eee;\n            padding-top: 15px;\n          \">本网站作品著作权归作者本人所有，凡发表在网站的文章，未经作者本人授权，不得转载。</p>\n        </div>`;\n\n    // 编者按和作者简介区域\n    if (data.editorNote || data.authorBio) {\n      richContent += `\n        <div style=\"\n          background: #fff;\n          padding: 25px;\n          margin: 25px 0;\n          border-radius: 12px;\n          border: 2px solid #e9ecef;\n          box-shadow: 0 4px 15px rgba(0,0,0,0.1);\n        \">`;\n\n      // 编者按\n      if (data.editorNote) {\n        // 清理编者按内容，去掉重复的标题\n        let cleanEditorNote = data.editorNote;\n        cleanEditorNote = cleanEditorNote.replace(/^【?编者按】?[：:]?\\s*/i, '');\n        cleanEditorNote = cleanEditorNote.replace(/^编者按[：:]?\\s*/i, '');\n\n        richContent += `\n          <div style=\"margin-bottom: 20px;\">\n            <h3 style=\"\n              font-size: 18px;\n              font-weight: bold;\n              color: #333;\n              margin: 0 0 10px 0;\n              border-bottom: 2px solid ${style.borderColor};\n              padding-bottom: 5px;\n              display: inline-block;\n            \">编者按</h3>\n            <p style=\"\n              margin: 0;\n              font-size: 15px;\n              color: #555;\n              line-height: 1.7;\n              text-indent: 0;\n            \">${cleanEditorNote}</p>\n          </div>`;\n      }\n\n      // 作者简介\n      if (data.authorBio) {\n        // 清理作者简介内容，去掉重复的\"作者：\"\n        let cleanAuthorBio = data.authorBio;\n        cleanAuthorBio = cleanAuthorBio.replace(/^作者[：:]?\\s*/i, '');\n\n        richContent += `\n          <div>\n            <h3 style=\"\n              font-size: 18px;\n              font-weight: bold;\n              color: #333;\n              margin: 0 0 15px 0;\n              border-bottom: 2px solid ${style.borderColor};\n              padding-bottom: 5px;\n              display: inline-block;\n            \">作者简介</h3>\n            <div style=\"display: flex; align-items: flex-start; gap: 15px;\">`;\n\n        if (data.authorPhoto) {\n          const proxiedPhoto = processImageUrl(data.authorPhoto);\n          richContent += `\n              <img src=\"${proxiedPhoto}\" alt=\"${data.author}\" style=\"\n                width: 80px;\n                height: 80px;\n                border-radius: 50%;\n                object-fit: cover;\n                border: 3px solid ${style.borderColor};\n                flex-shrink: 0;\n              \" />`;\n        }\n\n        richContent += `\n              <div>\n                <p style=\"\n                  margin: 0 0 5px 0;\n                  font-size: 16px;\n                  font-weight: bold;\n                  color: #333;\n                \">${data.author}</p>\n                <p style=\"\n                  margin: 0;\n                  font-size: 15px;\n                  color: #555;\n                  line-height: 1.7;\n                  text-indent: 0;\n                \">${cleanAuthorBio}</p>\n              </div>\n            </div>\n          </div>`;\n      }\n\n      richContent += `</div>`;\n    }\n\n    richContent += `</div>`;\n\n    return richContent;\n  };\n\n  const handleUrlExtract = async () => {\n    if (!url) return;\n\n    setIsLoading(true);\n    try {\n      const response = await fetch('/api/extract', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ url }),\n      });\n\n      if (!response.ok) {\n        throw new Error('提取失败');\n      }\n\n      const data = await response.json();\n\n      // 转换内容为富文本格式\n      const richContent = convertToRichText(data);\n      const processedData = {\n        ...data,\n        content: richContent\n      };\n\n      setExtractedData(processedData);\n    } catch (error) {\n      console.error('提取失败:', error);\n      alert('提取失败，请检查URL是否有效');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleContentPaste = () => {\n    if (!content) return;\n\n    // 将粘贴的内容转换为结构化数据\n    const lines = content.split('\\n').filter(line => line.trim());\n    const title = lines[0] || '粘贴的文章';\n    const contentText = lines.slice(1).join('\\n');\n\n    const pastedData = {\n      title,\n      author: '用户输入',\n      summary: contentText.substring(0, 200) + '...',\n      content: contentText,\n      images: [],\n      editorNote: ''\n    };\n\n    // 转换为富文本格式\n    const richContent = convertToRichText(pastedData);\n    const processedData = {\n      ...pastedData,\n      content: richContent\n    };\n\n    setExtractedData(processedData);\n  };\n\n  const handleAutoFormat = async () => {\n    if (!extractedData?.content) {\n      alert('请先提取或输入内容');\n      return;\n    }\n\n    setIsLoading(true);\n    try {\n      const response = await fetch('/api/format', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          content: extractedData.content,\n          styleId: selectedStyle\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error('格式化失败');\n      }\n\n      const data = await response.json();\n      setFormattedContent(data.formattedContent);\n\n      // 更新提取数据中的内容为格式化后的内容\n      setExtractedData({\n        ...extractedData,\n        content: data.formattedContent\n      });\n\n      alert(`已应用 ${data.styleName} 样式`);\n    } catch (error) {\n      console.error('自动排版失败:', error);\n      alert('自动排版失败，请重试');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleOriginalityCheck = async () => {\n    if (!extractedData?.content) {\n      alert('请先提取或输入内容');\n      return;\n    }\n\n    setIsCheckingOriginality(true);\n    try {\n      const response = await fetch('/api/originality', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          content: extractedData.content,\n          title: extractedData.title || ''\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error('原创检测失败');\n      }\n\n      const result = await response.json();\n      setOriginalityResult(result);\n    } catch (error) {\n      console.error('原创检测失败:', error);\n      alert('原创检测失败，请重试');\n    } finally {\n      setIsCheckingOriginality(false);\n    }\n  };\n\n  const handlePublishToWechat = () => {\n    // TODO: 实现微信公众号发布\n    console.log('发布到微信公众号');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-8\">\n      <div className=\"max-w-6xl mx-auto px-4\">\n        <header className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-2\">\n            微信公众号自动排版工具\n          </h1>\n          <p className=\"text-lg text-gray-600\">\n            智能提取、自动排版、原创检测、一键发布\n          </p>\n        </header>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-5 gap-8\">\n          {/* 左侧：输入和编辑区域 (3/5) */}\n          <div className=\"lg:col-span-3 space-y-6\">\n            {/* 标签页导航 */}\n            <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n              <div className=\"flex border-b border-gray-200\">\n                <button\n                  onClick={() => setActiveTab('url')}\n                  className={`flex-1 px-4 py-3 text-sm font-medium ${\n                    activeTab === 'url'\n                      ? 'bg-blue-600 text-white'\n                      : 'bg-gray-50 text-gray-700 hover:bg-gray-100'\n                  }`}\n                >\n                  URL导入\n                </button>\n                <button\n                  onClick={() => setActiveTab('paste')}\n                  className={`flex-1 px-4 py-3 text-sm font-medium ${\n                    activeTab === 'paste'\n                      ? 'bg-blue-600 text-white'\n                      : 'bg-gray-50 text-gray-700 hover:bg-gray-100'\n                  }`}\n                >\n                  文本粘贴\n                </button>\n                <button\n                  onClick={() => setActiveTab('editor')}\n                  className={`flex-1 px-4 py-3 text-sm font-medium ${\n                    activeTab === 'editor'\n                      ? 'bg-blue-600 text-white'\n                      : 'bg-gray-50 text-gray-700 hover:bg-gray-100'\n                  }`}\n                >\n                  富文本编辑\n                </button>\n              </div>\n\n              <div className=\"p-6\">\n                {/* URL输入 */}\n                {activeTab === 'url' && (\n                  <div className=\"space-y-4\">\n                    <input\n                      type=\"url\"\n                      value={url}\n                      onChange={(e) => setUrl(e.target.value)}\n                      placeholder=\"请输入文章URL\"\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    />\n                    <button\n                      onClick={handleUrlExtract}\n                      disabled={!url || isLoading}\n                      className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed\"\n                    >\n                      {isLoading ? '提取中...' : '提取内容'}\n                    </button>\n                  </div>\n                )}\n\n                {/* 内容粘贴 */}\n                {activeTab === 'paste' && (\n                  <div className=\"space-y-4\">\n                    <textarea\n                      value={content}\n                      onChange={(e) => setContent(e.target.value)}\n                      placeholder=\"请粘贴文章内容\"\n                      rows={8}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\"\n                    />\n                    <button\n                      onClick={handleContentPaste}\n                      disabled={!content}\n                      className=\"w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed\"\n                    >\n                      处理内容\n                    </button>\n                  </div>\n                )}\n\n                {/* 富文本编辑器 */}\n                {activeTab === 'editor' && (\n                  <div className=\"space-y-4\">\n                    <RichTextEditor\n                      content={richTextContent}\n                      onChange={setRichTextContent}\n                      placeholder=\"请输入或粘贴文章内容（支持图片、链接等富文本格式）\"\n                    />\n                    <button\n                      onClick={() => {\n                        // 将富文本内容转换为提取数据格式\n                        const tempDiv = document.createElement('div');\n                        tempDiv.innerHTML = richTextContent;\n                        const textContent = tempDiv.textContent || tempDiv.innerText || '';\n\n                        setExtractedData({\n                          title: '富文本编辑内容',\n                          author: '用户输入',\n                          summary: textContent.substring(0, 200) + '...',\n                          content: richTextContent,\n                          images: [],\n                          editorNote: ''\n                        });\n                      }}\n                      disabled={!richTextContent}\n                      className=\"w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed\"\n                    >\n                      使用富文本内容\n                    </button>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* 文章编辑器 */}\n            {extractedData && (\n              <div className=\"bg-white rounded-lg shadow-md p-6\">\n                <div className=\"flex justify-between items-center mb-4\">\n                  <h2 className=\"text-xl font-semibold\">文章编辑器</h2>\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"flex space-x-2 text-sm text-gray-600\">\n                      <span>作者：{extractedData.author}</span>\n                      {extractedData.images && extractedData.images.length > 0 && (\n                        <span>图片：{extractedData.images.length}张</span>\n                      )}\n                    </div>\n                    <div className=\"flex bg-gray-100 rounded-lg p-1\">\n                      <button\n                        onClick={() => setIsPreviewMode(false)}\n                        className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${\n                          !isPreviewMode\n                            ? 'bg-white text-blue-600 shadow-sm'\n                            : 'text-gray-600 hover:text-gray-900'\n                        }`}\n                      >\n                        编辑\n                      </button>\n                      <button\n                        onClick={() => setIsPreviewMode(true)}\n                        className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${\n                          isPreviewMode\n                            ? 'bg-white text-blue-600 shadow-sm'\n                            : 'text-gray-600 hover:text-gray-900'\n                        }`}\n                      >\n                        预览\n                      </button>\n                    </div>\n                  </div>\n                </div>\n\n                {/* 统一的富文本编辑器 */}\n                <div>\n                  {isPreviewMode ? (\n                    <div className=\"border border-gray-200 rounded-lg bg-white\">\n                      <div className=\"bg-gray-50 px-4 py-2 border-b border-gray-200 rounded-t-lg\">\n                        <span className=\"text-sm font-medium text-gray-700\">预览模式</span>\n                      </div>\n                      <div\n                        className=\"prose prose-lg max-w-none p-6 min-h-[500px]\"\n                        style={{\n                          fontFamily: '\"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", sans-serif',\n                          lineHeight: '1.8',\n                          color: '#333'\n                        }}\n                        dangerouslySetInnerHTML={{ __html: extractedData.content }}\n                      />\n                    </div>\n                  ) : (\n                    <RichTextEditor\n                      content={extractedData.content}\n                      onChange={(content) => setExtractedData({...extractedData, content})}\n                      placeholder=\"请输入完整的文章内容，包括标题、副标题、作者、摘要、正文、编者按等...\"\n                    />\n                  )}\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* 右侧：排版样式和操作区域 (2/5) */}\n          <div className=\"lg:col-span-2 space-y-6\">\n            {/* 排版样式选择 */}\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <h2 className=\"text-xl font-semibold mb-4\">排版样式</h2>\n              <div className=\"space-y-3\">\n                <select\n                  value={selectedStyle}\n                  onChange={(e) => setSelectedStyle(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                >\n                  {availableStyles.map(style => (\n                    <option key={style.id} value={style.id}>\n                      {style.name} - {style.description}\n                    </option>\n                  ))}\n                </select>\n                <button\n                  onClick={handleAutoFormat}\n                  disabled={!extractedData?.content || isLoading}\n                  className=\"w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed\"\n                >\n                  {isLoading ? '排版中...' : '应用排版样式'}\n                </button>\n              </div>\n            </div>\n\n            {/* 操作按钮 */}\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <h2 className=\"text-xl font-semibold mb-4\">操作</h2>\n              <div className=\"space-y-3\">\n                <button\n                  onClick={handleOriginalityCheck}\n                  disabled={!extractedData?.content || isCheckingOriginality}\n                  className=\"w-full bg-orange-600 text-white py-2 px-4 rounded-md hover:bg-orange-700 disabled:bg-gray-400 disabled:cursor-not-allowed\"\n                >\n                  {isCheckingOriginality ? '检测中...' : '原创检测'}\n                </button>\n                <button\n                  onClick={handlePublishToWechat}\n                  className=\"w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700\"\n                >\n                  发布到微信公众号\n                </button>\n                <button\n                  onClick={async () => {\n                    if (extractedData?.content) {\n                      try {\n                        // 创建一个临时的div来处理HTML内容\n                        const tempDiv = document.createElement('div');\n                        tempDiv.innerHTML = extractedData.content;\n\n                        // 处理图片：将代理URL转换回原始URL\n                        const images = tempDiv.querySelectorAll('img');\n                        images.forEach(img => {\n                          const src = img.src;\n                          if (src.includes('/api/image-proxy?url=')) {\n                            const originalUrl = decodeURIComponent(src.split('url=')[1]);\n                            img.src = originalUrl;\n                          }\n                        });\n\n                        // 创建富文本内容用于复制\n                        const htmlContent = tempDiv.innerHTML;\n                        const textContent = tempDiv.textContent || tempDiv.innerText || '';\n\n                        // 使用现代的Clipboard API复制富文本\n                        if (navigator.clipboard && window.ClipboardItem) {\n                          const clipboardItem = new ClipboardItem({\n                            'text/html': new Blob([htmlContent], { type: 'text/html' }),\n                            'text/plain': new Blob([textContent], { type: 'text/plain' })\n                          });\n                          await navigator.clipboard.write([clipboardItem]);\n                          alert('富文本内容（包含图片）已复制到剪贴板，可直接粘贴到微信公众号编辑器');\n                        } else {\n                          // 降级方案：只复制文本\n                          await navigator.clipboard.writeText(textContent);\n                          alert('文本内容已复制到剪贴板');\n                        }\n                      } catch (error) {\n                        console.error('复制失败:', error);\n                        // 最后的降级方案\n                        const textContent = extractedData.content.replace(/<[^>]*>/g, '');\n                        navigator.clipboard.writeText(textContent);\n                        alert('已复制纯文本内容到剪贴板');\n                      }\n                    }\n                  }}\n                  disabled={!extractedData?.content}\n                  className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed\"\n                >\n                  复制富文本内容\n                </button>\n                <button\n                  onClick={async () => {\n                    if (extractedData?.content) {\n                      try {\n                        // 生成兼容性更好的富文本内容\n                        const compatibleContent = convertToCompatibleRichText(extractedData);\n\n                        // 创建临时div来处理内容\n                        const tempDiv = document.createElement('div');\n                        tempDiv.innerHTML = compatibleContent;\n\n                        // 创建富文本内容用于复制\n                        const htmlContent = tempDiv.innerHTML;\n                        const textContent = tempDiv.textContent || tempDiv.innerText || '';\n\n                        // 使用现代的Clipboard API复制富文本\n                        if (navigator.clipboard && window.ClipboardItem) {\n                          const clipboardItem = new ClipboardItem({\n                            'text/html': new Blob([htmlContent], { type: 'text/html' }),\n                            'text/plain': new Blob([textContent], { type: 'text/plain' })\n                          });\n                          await navigator.clipboard.write([clipboardItem]);\n                          alert('兼容模式富文本内容已复制到剪贴板，可直接粘贴到其他公众号编辑器');\n                        } else {\n                          // 降级方案：只复制文本\n                          await navigator.clipboard.writeText(textContent);\n                          alert('文本内容已复制到剪贴板');\n                        }\n                      } catch (error) {\n                        console.error('复制失败:', error);\n                        // 最后的降级方案\n                        const textContent = extractedData.content.replace(/<[^>]*>/g, '');\n                        navigator.clipboard.writeText(textContent);\n                        alert('已复制纯文本内容到剪贴板');\n                      }\n                    }\n                  }}\n                  disabled={!extractedData?.content}\n                  className=\"w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed\"\n                >\n                  复制兼容模式内容\n                </button>\n              </div>\n            </div>\n\n            {/* 原创检测结果 */}\n            {originalityResult && (\n              <div className=\"bg-white rounded-lg shadow-md p-6\">\n                <h2 className=\"text-xl font-semibold mb-4\">原创检测结果</h2>\n                <div className=\"space-y-4\">\n                  {/* 检测状态 */}\n                  <div className={`p-4 rounded-lg ${\n                    originalityResult.isOriginal\n                      ? 'bg-green-50 border border-green-200'\n                      : 'bg-red-50 border border-red-200'\n                  }`}>\n                    <div className=\"flex items-center justify-between\">\n                      <span className={`font-semibold ${\n                        originalityResult.isOriginal ? 'text-green-800' : 'text-red-800'\n                      }`}>\n                        {originalityResult.isOriginal ? '✅ 原创内容' : '❌ 疑似非原创'}\n                      </span>\n                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${\n                        originalityResult.riskLevel === 'low'\n                          ? 'bg-green-100 text-green-800'\n                          : originalityResult.riskLevel === 'medium'\n                          ? 'bg-yellow-100 text-yellow-800'\n                          : 'bg-red-100 text-red-800'\n                      }`}>\n                        {originalityResult.riskLevel === 'low' ? '低风险' :\n                         originalityResult.riskLevel === 'medium' ? '中风险' : '高风险'}\n                      </span>\n                    </div>\n                  </div>\n\n                  {/* 相似度信息 */}\n                  <div className=\"grid grid-cols-2 gap-4\">\n                    <div className=\"bg-gray-50 p-3 rounded-lg\">\n                      <div className=\"text-sm text-gray-600\">相似度</div>\n                      <div className=\"text-2xl font-bold text-gray-900\">\n                        {originalityResult.similarity}%\n                      </div>\n                    </div>\n                    <div className=\"bg-gray-50 p-3 rounded-lg\">\n                      <div className=\"text-sm text-gray-600\">重复文章数</div>\n                      <div className=\"text-2xl font-bold text-gray-900\">\n                        {originalityResult.duplicateCount}\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* 建议 */}\n                  <div>\n                    <h3 className=\"font-semibold text-gray-900 mb-2\">检测建议</h3>\n                    <ul className=\"space-y-1\">\n                      {originalityResult.suggestions.map((suggestion: string, index: number) => (\n                        <li key={index} className=\"text-sm text-gray-700 flex items-start\">\n                          <span className=\"mr-2\">•</span>\n                          <span>{suggestion}</span>\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* 预览区域 */}\n            {formattedContent && (\n              <div className=\"bg-white rounded-lg shadow-md p-6\">\n                <h2 className=\"text-xl font-semibold mb-4\">排版预览</h2>\n                <div\n                  className=\"border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto\"\n                  dangerouslySetInnerHTML={{ __html: formattedContent }}\n                />\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACxD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IACvE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAChE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAChE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,YAAY;IACZ,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,mBAAmB,KAAK,MAAM;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,YAAY;IACZ,6JAAA,CAAA,UAAK,CAAC,SAAS;0BAAC;YACd;QACF;yBAAG,EAAE;IAEL,qBAAqB;IACrB,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,KAAK,OAAO;QAEjB,oBAAoB;QACpB,IAAI,IAAI,UAAU,CAAC,QAAQ,CAAC,IAAI,UAAU,CAAC,OAAO;YAChD,OAAO;QACT;QAEA,oBAAoB;QACpB,IAAI,IAAI,UAAU,CAAC,OAAO;YACxB,MAAM,WAAW;QACnB;QAEA,iBAAiB;QACjB,OAAO,AAAC,wBAA+C,OAAxB,mBAAmB;IACpD;IAEA,4BAA4B;IAC5B,MAAM,8BAA8B,CAAC;QACnC,IAAI,cAAc,AAAC,w0BAiBoB,OAArB,KAAK,KAAK,IAAI,QAAO;QAEvC,MAAM;QACN,IAAI,KAAK,QAAQ,EAAE;YACjB,eAAe,AAAC,+PAOc,OAAd,KAAK,QAAQ,EAAC;QAChC;QAEA,OAAO;QACP,IAAI,KAAK,MAAM,EAAE;YACf,eAAe,AAAC,0NAMe,OAAZ,KAAK,MAAM,EAAC;QACjC;QAEA,eAAgB;QAKhB,KAAK;QACL,IAAI,KAAK,OAAO,IAAI,KAAK,UAAU,EAAE;YACnC,eAAe,AAAC,yxBAmBa,OAAb,KAAK,OAAO,EAAC;QAI/B;QAEA,SAAS;QACT,IAAI,cAAc;QAClB,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,QAAQ,CAAC,MAAM;YAC9C,MAAM,UAAU,SAAS,aAAa,CAAC;YACvC,QAAQ,SAAS,GAAG,KAAK,OAAO;YAChC,MAAM,aAAa,MAAM,IAAI,CAAC,QAAQ,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAA;oBAAK;wBAAA,iBAAA,EAAE,WAAW,cAAb,qCAAA,eAAe,IAAI;eAAI,MAAM,CAAC;YACpG,cAAc,WAAW,IAAI,CAAC;QAChC,OAAO;YACL,cAAc,KAAK,OAAO,IAAI;QAChC;QAEA,qBAAqB;QACrB,cAAc,YAAY,OAAO,CAAC,6CAA6C,IAAI,IAAI;QAEvF,SAAS;QACT,eAAgB;QAKhB,YAAY;QACZ,MAAM,aAAa,YAAY,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI;QAC7D,MAAM,SAAS,KAAK,MAAM,IAAI,EAAE;QAEhC,IAAI,OAAO,MAAM,GAAG,GAAG;YACrB,MAAM,iBAAiB,EAAE;YACzB,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,WAAW,MAAM,GAAG,OAAO,MAAM;YACzE,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;gBACtC,eAAe,IAAI,CAAC,KAAK,GAAG,CAAC,WAAW,MAAM,EAAE,CAAC,IAAI,CAAC,IAAI;YAC5D;YAEA,IAAI,aAAa;YACjB,IAAK,IAAI,IAAI,GAAG,KAAK,WAAW,MAAM,EAAE,IAAK;gBAC3C,IAAI,eAAe,QAAQ,CAAC,MAAM,aAAa,OAAO,MAAM,EAAE;oBAC5D,MAAM,aAAa,gBAAgB,MAAM,CAAC,WAAW;oBACrD,eAAe,AAAC,oNAIiC,OAAvB,YAAW,cAA2B,OAAf,aAAa,GAAE;oBAQhE;gBACF;gBAEA,IAAI,IAAI,WAAW,MAAM,EAAE;oBACzB,eAAe,AAAC,gPAOiB,OAArB,UAAU,CAAC,EAAE,CAAC,IAAI,IAAG;gBACnC;YACF;YAEA,MAAO,aAAa,OAAO,MAAM,CAAE;gBACjC,MAAM,aAAa,gBAAgB,MAAM,CAAC,WAAW;gBACrD,eAAe,AAAC,oNAImC,OAAvB,YAAW,cAA2B,OAAf,aAAa,GAAE;gBAQlE;YACF;QACF,OAAO;YACL,WAAW,OAAO,CAAC,CAAA;gBACjB,eAAe,AAAC,gPAOe,OAAjB,UAAU,IAAI,IAAG;YACjC;QACF;QAEA,OAAO;QACP,eAAgB;QAchB,aAAa;QACb,IAAI,KAAK,UAAU,IAAI,KAAK,SAAS,EAAE;YACrC,eAAgB;YAKhB,IAAI,KAAK,UAAU,EAAE;gBACnB,IAAI,kBAAkB,KAAK,UAAU;gBACrC,kBAAkB,gBAAgB,OAAO,CAAC,qBAAqB;gBAC/D,kBAAkB,gBAAgB,OAAO,CAAC,iBAAiB;gBAE3D,eAAe,AAAC,ulBAgBc,OAAhB,iBAAgB;YAChC;YAEA,IAAI,KAAK,SAAS,EAAE;gBAClB,IAAI,iBAAiB,KAAK,SAAS;gBACnC,iBAAiB,eAAe,OAAO,CAAC,gBAAgB;gBAExD,eAAgB;gBAahB,IAAI,KAAK,WAAW,EAAE;oBACpB,MAAM,eAAe,gBAAgB,KAAK,WAAW;oBACrD,eAAe,AAAC,kIAEgC,OAAtB,cAAa,WAAqB,OAAZ,KAAK,MAAM,EAAC;gBAM9D;gBAEA,eAAe,AAAC,4SAcI,OAPA,KAAK,MAAM,EAAC,0RAOG,OAAf,gBAAe;YAIrC;YAEA,eAAgB;QAIlB;QAEA,eAAgB;QAKhB,OAAO;IACT;IAEA,YAAY;IACZ,MAAM,uBAAuB;QAC3B,MAAM,SAAS;YACb;gBACE,YAAY;gBACZ,WAAW;gBACX,aAAa;YACf;YACA;gBACE,YAAY;gBACZ,WAAW;gBACX,aAAa;YACf;YACA;gBACE,YAAY;gBACZ,WAAW;gBACX,aAAa;YACf;YACA;gBACE,YAAY;gBACZ,WAAW;gBACX,aAAa;YACf;YACA;gBACE,YAAY;gBACZ,WAAW;gBACX,aAAa;YACf;SACD;QACD,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;IAC1D;IAEA,iBAAiB;IACjB,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ;QAEd,IAAI,cAAc,AAAC,wbAUF,OADK,MAAM,UAAU,EAAC,0BAEX,OADX,MAAM,SAAS,EAAC,qCAGvB,OAFkB,MAAM,WAAW,EAAC,uDAEf,OAArB,KAAK,KAAK,IAAI,QAAO;QAE/B,IAAI,KAAK,QAAQ,EAAE;YACjB,eAAe,AAAC,mKAMM,OAAd,KAAK,QAAQ,EAAC;QACxB;QAEA,eAAe,AAAC,kKAMmB,OAAtB,KAAK,MAAM,IAAI,QAAO;QAGnC,oCAAoC;QACpC,IAAI,KAAK,UAAU,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,IAAI,IAAI;YAC1D,oBAAoB;YACpB,IAAI,eAAe,KAAK,OAAO,CAAC,IAAI;YACpC,eAAe,aAAa,OAAO,CAAC,gBAAgB;YAEpD,oBAAoB;YACpB,IAAI,aAAa,IAAI,IAAI;gBACvB,eAAe,AAAC,iJAeU,OAVC,MAAM,WAAW,EAAC,gTAUN,OAAb,cAAa;YAEzC;QACF;QAEA,WAAW;QACX,eAAe,AAAC,4IAK4B,OAAlB,MAAM,WAAW,EAAC;QAG5C,SAAS;QACT,IAAI,cAAc;QAClB,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,QAAQ,CAAC,MAAM;YAC9C,oBAAoB;YACpB,MAAM,UAAU,SAAS,aAAa,CAAC;YACvC,QAAQ,SAAS,GAAG,KAAK,OAAO;YAChC,MAAM,aAAa,MAAM,IAAI,CAAC,QAAQ,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAA;oBAAK;wBAAA,iBAAA,EAAE,WAAW,cAAb,qCAAA,eAAe,IAAI;eAAI,MAAM,CAAC;YACpG,cAAc,WAAW,IAAI,CAAC;QAChC,OAAO;YACL,cAAc,KAAK,OAAO,IAAI;QAChC;QAEA,qBAAqB;QACrB,cAAc,YAAY,OAAO,CAAC,6CAA6C,IAAI,IAAI;QAEvF,iBAAiB;QACjB,MAAM,aAAa,YAAY,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI;QAC7D,MAAM,SAAS,KAAK,MAAM,IAAI,EAAE;QAEhC,IAAI,OAAO,MAAM,GAAG,KAAK,WAAW,MAAM,GAAG,GAAG;YAC9C,wBAAwB;YACxB,MAAM,aAAa,WAAW,MAAM,GAAG,GAAG,cAAc;YACxD,MAAM,iBAA2B,EAAE;YAEnC,IAAI,OAAO,MAAM,IAAI,YAAY;gBAC/B,yBAAyB;gBACzB,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;oBACnC,eAAe,IAAI,CAAC;gBACtB;YACF,OAAO;gBACL,mBAAmB;gBACnB,MAAM,OAAO,aAAa,OAAO,MAAM;gBACvC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;oBACtC,MAAM,WAAW,KAAK,KAAK,CAAC,IAAI;oBAChC,eAAe,IAAI,CAAC;gBACtB;YACF;YAEA,iBAAiB;YACjB,IAAI,aAAa;YACjB,IAAK,IAAI,IAAI,GAAG,KAAK,WAAW,MAAM,EAAE,IAAK;gBAC3C,kBAAkB;gBAClB,IAAI,eAAe,QAAQ,CAAC,MAAM,aAAa,OAAO,MAAM,EAAE;oBAC5D,MAAM,aAAa,gBAAgB,MAAM,CAAC,WAAW;oBACrD,eAAe,AAAC,wFAEqB,OAAvB,YAAW,cAGD,OAHa,aAAa,GAAE,4GAGV,OAAlB,MAAM,WAAW,EAAC;oBAG1C;gBACF;gBAEA,mBAAmB;gBACnB,IAAI,IAAI,WAAW,MAAM,EAAE;oBACzB,eAAe,AAAC,wLAOS,OAArB,UAAU,CAAC,EAAE,CAAC,IAAI,IAAG;gBAC3B;YACF;YAEA,iBAAiB;YACjB,MAAO,aAAa,OAAO,MAAM,CAAE;gBACjC,MAAM,aAAa,gBAAgB,MAAM,CAAC,WAAW;gBACrD,eAAe,AAAC,wFAEuB,OAAvB,YAAW,cAGD,OAHa,aAAa,GAAE,4GAGV,OAAlB,MAAM,WAAW,EAAC;gBAG5C;YACF;QACF,OAAO;YACL,cAAc;YACd,WAAW,OAAO,CAAC,CAAA;gBACjB,eAAe,AAAC,wLAOO,OAAjB,UAAU,IAAI,IAAG;YACzB;QACF;QAEA,OAAO;QACP,eAAgB;QAYhB,aAAa;QACb,IAAI,KAAK,UAAU,IAAI,KAAK,SAAS,EAAE;YACrC,eAAgB;YAUhB,MAAM;YACN,IAAI,KAAK,UAAU,EAAE;gBACnB,kBAAkB;gBAClB,IAAI,kBAAkB,KAAK,UAAU;gBACrC,kBAAkB,gBAAgB,OAAO,CAAC,qBAAqB;gBAC/D,kBAAkB,gBAAgB,OAAO,CAAC,iBAAiB;gBAE3D,eAAe,AAAC,oPAiBR,OAVyB,MAAM,WAAW,EAAC,qSAU3B,OAAhB,iBAAgB;YAE1B;YAEA,OAAO;YACP,IAAI,KAAK,SAAS,EAAE;gBAClB,sBAAsB;gBACtB,IAAI,iBAAiB,KAAK,SAAS;gBACnC,iBAAiB,eAAe,OAAO,CAAC,gBAAgB;gBAExD,eAAe,AAAC,uNAOmC,OAAlB,MAAM,WAAW,EAAC;gBAMnD,IAAI,KAAK,WAAW,EAAE;oBACpB,MAAM,eAAe,gBAAgB,KAAK,WAAW;oBACrD,eAAe,AAAC,6BACsB,OAAtB,cAAa,WAKH,OALY,KAAK,MAAM,EAAC,uLAKN,OAAlB,MAAM,WAAW,EAAC;gBAG9C;gBAEA,eAAe,AAAC,wNAcJ,OAPA,KAAK,MAAM,EAAC,kOAOG,OAAf,gBAAe;YAI7B;YAEA,eAAgB;QAClB;QAEA,eAAgB;QAEhB,OAAO;IACT;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,KAAK;QAEV,aAAa;QACb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAI;YAC7B;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,aAAa;YACb,MAAM,cAAc,kBAAkB;YACtC,MAAM,gBAAgB;gBACpB,GAAG,IAAI;gBACP,SAAS;YACX;YAEA,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;YACvB,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,SAAS;QAEd,iBAAiB;QACjB,MAAM,QAAQ,QAAQ,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI;QAC1D,MAAM,QAAQ,KAAK,CAAC,EAAE,IAAI;QAC1B,MAAM,cAAc,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC;QAExC,MAAM,aAAa;YACjB;YACA,QAAQ;YACR,SAAS,YAAY,SAAS,CAAC,GAAG,OAAO;YACzC,SAAS;YACT,QAAQ,EAAE;YACV,YAAY;QACd;QAEA,WAAW;QACX,MAAM,cAAc,kBAAkB;QACtC,MAAM,gBAAgB;YACpB,GAAG,UAAU;YACb,SAAS;QACX;QAEA,iBAAiB;IACnB;IAEA,MAAM,mBAAmB;QACvB,IAAI,EAAC,0BAAA,oCAAA,cAAe,OAAO,GAAE;YAC3B,MAAM;YACN;QACF;QAEA,aAAa;QACb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS,cAAc,OAAO;oBAC9B,SAAS;gBACX;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,oBAAoB,KAAK,gBAAgB;YAEzC,qBAAqB;YACrB,iBAAiB;gBACf,GAAG,aAAa;gBAChB,SAAS,KAAK,gBAAgB;YAChC;YAEA,MAAM,AAAC,OAAqB,OAAf,KAAK,SAAS,EAAC;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,yBAAyB;QAC7B,IAAI,EAAC,0BAAA,oCAAA,cAAe,OAAO,GAAE;YAC3B,MAAM;YACN;QACF;QAEA,yBAAyB;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS,cAAc,OAAO;oBAC9B,OAAO,cAAc,KAAK,IAAI;gBAChC;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,qBAAqB;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;QACR,SAAU;YACR,yBAAyB;QAC3B;IACF;IAEA,MAAM,wBAAwB;QAC5B,kBAAkB;QAClB,QAAQ,GAAG,CAAC;IACd;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAO,WAAU;;sCAChB,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAKvC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS,IAAM,aAAa;oDAC5B,WAAW,AAAC,wCAIX,OAHC,cAAc,QACV,2BACA;8DAEP;;;;;;8DAGD,6LAAC;oDACC,SAAS,IAAM,aAAa;oDAC5B,WAAW,AAAC,wCAIX,OAHC,cAAc,UACV,2BACA;8DAEP;;;;;;8DAGD,6LAAC;oDACC,SAAS,IAAM,aAAa;oDAC5B,WAAW,AAAC,wCAIX,OAHC,cAAc,WACV,2BACA;8DAEP;;;;;;;;;;;;sDAKH,6LAAC;4CAAI,WAAU;;gDAEZ,cAAc,uBACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,OAAO,EAAE,MAAM,CAAC,KAAK;4DACtC,aAAY;4DACZ,WAAU;;;;;;sEAEZ,6LAAC;4DACC,SAAS;4DACT,UAAU,CAAC,OAAO;4DAClB,WAAU;sEAET,YAAY,WAAW;;;;;;;;;;;;gDAM7B,cAAc,yBACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,OAAO;4DACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4DAC1C,aAAY;4DACZ,MAAM;4DACN,WAAU;;;;;;sEAEZ,6LAAC;4DACC,SAAS;4DACT,UAAU,CAAC;4DACX,WAAU;sEACX;;;;;;;;;;;;gDAOJ,cAAc,0BACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uIAAA,CAAA,UAAc;4DACb,SAAS;4DACT,UAAU;4DACV,aAAY;;;;;;sEAEd,6LAAC;4DACC,SAAS;gEACP,kBAAkB;gEAClB,MAAM,UAAU,SAAS,aAAa,CAAC;gEACvC,QAAQ,SAAS,GAAG;gEACpB,MAAM,cAAc,QAAQ,WAAW,IAAI,QAAQ,SAAS,IAAI;gEAEhE,iBAAiB;oEACf,OAAO;oEACP,QAAQ;oEACR,SAAS,YAAY,SAAS,CAAC,GAAG,OAAO;oEACzC,SAAS;oEACT,QAAQ,EAAE;oEACV,YAAY;gEACd;4DACF;4DACA,UAAU,CAAC;4DACX,WAAU;sEACX;;;;;;;;;;;;;;;;;;;;;;;;gCASR,+BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAwB;;;;;;8DACtC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;wEAAK;wEAAI,cAAc,MAAM;;;;;;;gEAC7B,cAAc,MAAM,IAAI,cAAc,MAAM,CAAC,MAAM,GAAG,mBACrD,6LAAC;;wEAAK;wEAAI,cAAc,MAAM,CAAC,MAAM;wEAAC;;;;;;;;;;;;;sEAG1C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,SAAS,IAAM,iBAAiB;oEAChC,WAAW,AAAC,8DAIX,OAHC,CAAC,gBACG,qCACA;8EAEP;;;;;;8EAGD,6LAAC;oEACC,SAAS,IAAM,iBAAiB;oEAChC,WAAW,AAAC,8DAIX,OAHC,gBACI,qCACA;8EAEP;;;;;;;;;;;;;;;;;;;;;;;;sDAQP,6LAAC;sDACE,8BACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEAAoC;;;;;;;;;;;kEAEtD,6LAAC;wDACC,WAAU;wDACV,OAAO;4DACL,YAAY;4DACZ,YAAY;4DACZ,OAAO;wDACT;wDACA,yBAAyB;4DAAE,QAAQ,cAAc,OAAO;wDAAC;;;;;;;;;;;qEAI7D,6LAAC,uIAAA,CAAA,UAAc;gDACb,SAAS,cAAc,OAAO;gDAC9B,UAAU,CAAC,UAAY,iBAAiB;wDAAC,GAAG,aAAa;wDAAE;oDAAO;gDAClE,aAAY;;;;;;;;;;;;;;;;;;;;;;;sCASxB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAChD,WAAU;8DAET,gBAAgB,GAAG,CAAC,CAAA,sBACnB,6LAAC;4DAAsB,OAAO,MAAM,EAAE;;gEACnC,MAAM,IAAI;gEAAC;gEAAI,MAAM,WAAW;;2DADtB,MAAM,EAAE;;;;;;;;;;8DAKzB,6LAAC;oDACC,SAAS;oDACT,UAAU,EAAC,0BAAA,oCAAA,cAAe,OAAO,KAAI;oDACrC,WAAU;8DAET,YAAY,WAAW;;;;;;;;;;;;;;;;;;8CAM9B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS;oDACT,UAAU,EAAC,0BAAA,oCAAA,cAAe,OAAO,KAAI;oDACrC,WAAU;8DAET,wBAAwB,WAAW;;;;;;8DAEtC,6LAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;8DAGD,6LAAC;oDACC,SAAS;wDACP,IAAI,0BAAA,oCAAA,cAAe,OAAO,EAAE;4DAC1B,IAAI;gEACF,sBAAsB;gEACtB,MAAM,UAAU,SAAS,aAAa,CAAC;gEACvC,QAAQ,SAAS,GAAG,cAAc,OAAO;gEAEzC,sBAAsB;gEACtB,MAAM,SAAS,QAAQ,gBAAgB,CAAC;gEACxC,OAAO,OAAO,CAAC,CAAA;oEACb,MAAM,MAAM,IAAI,GAAG;oEACnB,IAAI,IAAI,QAAQ,CAAC,0BAA0B;wEACzC,MAAM,cAAc,mBAAmB,IAAI,KAAK,CAAC,OAAO,CAAC,EAAE;wEAC3D,IAAI,GAAG,GAAG;oEACZ;gEACF;gEAEA,cAAc;gEACd,MAAM,cAAc,QAAQ,SAAS;gEACrC,MAAM,cAAc,QAAQ,WAAW,IAAI,QAAQ,SAAS,IAAI;gEAEhE,0BAA0B;gEAC1B,IAAI,UAAU,SAAS,IAAI,OAAO,aAAa,EAAE;oEAC/C,MAAM,gBAAgB,IAAI,cAAc;wEACtC,aAAa,IAAI,KAAK;4EAAC;yEAAY,EAAE;4EAAE,MAAM;wEAAY;wEACzD,cAAc,IAAI,KAAK;4EAAC;yEAAY,EAAE;4EAAE,MAAM;wEAAa;oEAC7D;oEACA,MAAM,UAAU,SAAS,CAAC,KAAK,CAAC;wEAAC;qEAAc;oEAC/C,MAAM;gEACR,OAAO;oEACL,aAAa;oEACb,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;oEACpC,MAAM;gEACR;4DACF,EAAE,OAAO,OAAO;gEACd,QAAQ,KAAK,CAAC,SAAS;gEACvB,UAAU;gEACV,MAAM,cAAc,cAAc,OAAO,CAAC,OAAO,CAAC,YAAY;gEAC9D,UAAU,SAAS,CAAC,SAAS,CAAC;gEAC9B,MAAM;4DACR;wDACF;oDACF;oDACA,UAAU,EAAC,0BAAA,oCAAA,cAAe,OAAO;oDACjC,WAAU;8DACX;;;;;;8DAGD,6LAAC;oDACC,SAAS;wDACP,IAAI,0BAAA,oCAAA,cAAe,OAAO,EAAE;4DAC1B,IAAI;gEACF,gBAAgB;gEAChB,MAAM,oBAAoB,4BAA4B;gEAEtD,eAAe;gEACf,MAAM,UAAU,SAAS,aAAa,CAAC;gEACvC,QAAQ,SAAS,GAAG;gEAEpB,cAAc;gEACd,MAAM,cAAc,QAAQ,SAAS;gEACrC,MAAM,cAAc,QAAQ,WAAW,IAAI,QAAQ,SAAS,IAAI;gEAEhE,0BAA0B;gEAC1B,IAAI,UAAU,SAAS,IAAI,OAAO,aAAa,EAAE;oEAC/C,MAAM,gBAAgB,IAAI,cAAc;wEACtC,aAAa,IAAI,KAAK;4EAAC;yEAAY,EAAE;4EAAE,MAAM;wEAAY;wEACzD,cAAc,IAAI,KAAK;4EAAC;yEAAY,EAAE;4EAAE,MAAM;wEAAa;oEAC7D;oEACA,MAAM,UAAU,SAAS,CAAC,KAAK,CAAC;wEAAC;qEAAc;oEAC/C,MAAM;gEACR,OAAO;oEACL,aAAa;oEACb,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;oEACpC,MAAM;gEACR;4DACF,EAAE,OAAO,OAAO;gEACd,QAAQ,KAAK,CAAC,SAAS;gEACvB,UAAU;gEACV,MAAM,cAAc,cAAc,OAAO,CAAC,OAAO,CAAC,YAAY;gEAC9D,UAAU,SAAS,CAAC,SAAS,CAAC;gEAC9B,MAAM;4DACR;wDACF;oDACF;oDACA,UAAU,EAAC,0BAAA,oCAAA,cAAe,OAAO;oDACjC,WAAU;8DACX;;;;;;;;;;;;;;;;;;gCAOJ,mCACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAW,AAAC,kBAIhB,OAHC,kBAAkB,UAAU,GACxB,wCACA;8DAEJ,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAW,AAAC,iBAEjB,OADC,kBAAkB,UAAU,GAAG,mBAAmB;0EAEjD,kBAAkB,UAAU,GAAG,WAAW;;;;;;0EAE7C,6LAAC;gEAAK,WAAW,AAAC,8CAMjB,OALC,kBAAkB,SAAS,KAAK,QAC5B,gCACA,kBAAkB,SAAS,KAAK,WAChC,kCACA;0EAEH,kBAAkB,SAAS,KAAK,QAAQ,QACxC,kBAAkB,SAAS,KAAK,WAAW,QAAQ;;;;;;;;;;;;;;;;;8DAM1D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;8EACvC,6LAAC;oEAAI,WAAU;;wEACZ,kBAAkB,UAAU;wEAAC;;;;;;;;;;;;;sEAGlC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;8EACvC,6LAAC;oEAAI,WAAU;8EACZ,kBAAkB,cAAc;;;;;;;;;;;;;;;;;;8DAMvC,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,6LAAC;4DAAG,WAAU;sEACX,kBAAkB,WAAW,CAAC,GAAG,CAAC,CAAC,YAAoB,sBACtD,6LAAC;oEAAe,WAAU;;sFACxB,6LAAC;4EAAK,WAAU;sFAAO;;;;;;sFACvB,6LAAC;sFAAM;;;;;;;mEAFA;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAYpB,kCACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CACC,WAAU;4CACV,yBAAyB;gDAAE,QAAQ;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStE;GApqCwB;KAAA", "debugId": null}}]}