(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/RichTextEditor.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$react$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/react/dist/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$starter$2d$kit$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/starter-kit/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$image$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-image/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$link$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-link/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
const RichTextEditor = (param)=>{
    let { content, onChange, placeholder = '请输入内容...' } = param;
    _s();
    const [isMounted, setIsMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RichTextEditor.useEffect": ()=>{
            setIsMounted(true);
        }
    }["RichTextEditor.useEffect"], []);
    const editor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$react$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useEditor"])({
        extensions: [
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$starter$2d$kit$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$image$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].configure({
                HTMLAttributes: {
                    class: 'max-w-full h-auto rounded-lg',
                    style: 'max-width: 100%; height: auto; margin: 15px auto; border-radius: 8px; display: block; box-shadow: 0 4px 12px rgba(0,0,0,0.1);'
                },
                allowBase64: true,
                inline: false
            }),
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$link$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].configure({
                openOnClick: false,
                HTMLAttributes: {
                    class: 'text-blue-600 underline'
                }
            })
        ],
        content,
        onUpdate: {
            "RichTextEditor.useEditor[editor]": (param)=>{
                let { editor } = param;
                onChange(editor.getHTML());
            }
        }["RichTextEditor.useEditor[editor]"],
        editorProps: {
            attributes: {
                class: 'prose prose-lg max-w-none focus:outline-none min-h-[400px] p-6 leading-relaxed',
                style: 'font-family: "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif; line-height: 1.8; color: #333;'
            }
        },
        immediatelyRender: false
    });
    // 当content prop变化时，更新编辑器内容
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RichTextEditor.useEffect": ()=>{
            if (editor && content !== editor.getHTML()) {
                editor.commands.setContent(content);
            }
        }
    }["RichTextEditor.useEffect"], [
        content,
        editor
    ]);
    const addImage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "RichTextEditor.useCallback[addImage]": ()=>{
            const url = window.prompt('请输入图片URL:');
            if (url && editor) {
                editor.chain().focus().setImage({
                    src: url
                }).run();
            }
        }
    }["RichTextEditor.useCallback[addImage]"], [
        editor
    ]);
    const uploadImage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "RichTextEditor.useCallback[uploadImage]": ()=>{
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = ({
                "RichTextEditor.useCallback[uploadImage]": async (e)=>{
                    var _files;
                    const file = (_files = e.target.files) === null || _files === void 0 ? void 0 : _files[0];
                    if (file && editor) {
                        try {
                            const formData = new FormData();
                            formData.append('image', file);
                            const response = await fetch('/api/upload-image', {
                                method: 'POST',
                                body: formData
                            });
                            if (response.ok) {
                                const result = await response.json();
                                editor.chain().focus().setImage({
                                    src: result.url
                                }).run();
                            } else {
                                const error = await response.json();
                                alert("上传失败: ".concat(error.error));
                            }
                        } catch (error) {
                            console.error('图片上传失败:', error);
                            alert('图片上传失败，请重试');
                        }
                    }
                }
            })["RichTextEditor.useCallback[uploadImage]"];
            input.click();
        }
    }["RichTextEditor.useCallback[uploadImage]"], [
        editor
    ]);
    const setLink = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "RichTextEditor.useCallback[setLink]": ()=>{
            const previousUrl = editor === null || editor === void 0 ? void 0 : editor.getAttributes('link').href;
            const url = window.prompt('请输入链接URL:', previousUrl);
            if (url === null) {
                return;
            }
            if (url === '') {
                editor === null || editor === void 0 ? void 0 : editor.chain().focus().extendMarkRange('link').unsetLink().run();
                return;
            }
            editor === null || editor === void 0 ? void 0 : editor.chain().focus().extendMarkRange('link').setLink({
                href: url
            }).run();
        }
    }["RichTextEditor.useCallback[setLink]"], [
        editor
    ]);
    if (!isMounted) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "border border-gray-300 rounded-lg overflow-hidden",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "border-b border-gray-300 p-2 bg-gray-50",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "h-8 bg-gray-200 rounded animate-pulse"
                    }, void 0, false, {
                        fileName: "[project]/src/components/RichTextEditor.tsx",
                        lineNumber: 123,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/RichTextEditor.tsx",
                    lineNumber: 122,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white p-6 min-h-[400px] flex items-center justify-center",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-gray-500",
                        children: "加载编辑器中..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/RichTextEditor.tsx",
                        lineNumber: 126,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/RichTextEditor.tsx",
                    lineNumber: 125,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/RichTextEditor.tsx",
            lineNumber: 121,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0));
    }
    if (!editor) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "border border-gray-300 rounded-lg overflow-hidden",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white p-6 min-h-[400px] flex items-center justify-center",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-gray-500",
                    children: "编辑器初始化中..."
                }, void 0, false, {
                    fileName: "[project]/src/components/RichTextEditor.tsx",
                    lineNumber: 136,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/RichTextEditor.tsx",
                lineNumber: 135,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        }, void 0, false, {
            fileName: "[project]/src/components/RichTextEditor.tsx",
            lineNumber: 134,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0));
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "border border-gray-300 rounded-lg overflow-hidden",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border-b border-gray-300 p-2 bg-gray-50 flex flex-wrap gap-1",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>editor.chain().focus().toggleBold().run(),
                        disabled: !editor.can().chain().focus().toggleBold().run(),
                        className: "px-3 py-1 rounded text-sm font-medium ".concat(editor.isActive('bold') ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100', " border border-gray-300"),
                        children: "粗体"
                    }, void 0, false, {
                        fileName: "[project]/src/components/RichTextEditor.tsx",
                        lineNumber: 146,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>editor.chain().focus().toggleItalic().run(),
                        disabled: !editor.can().chain().focus().toggleItalic().run(),
                        className: "px-3 py-1 rounded text-sm font-medium ".concat(editor.isActive('italic') ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100', " border border-gray-300"),
                        children: "斜体"
                    }, void 0, false, {
                        fileName: "[project]/src/components/RichTextEditor.tsx",
                        lineNumber: 157,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>editor.chain().focus().toggleStrike().run(),
                        disabled: !editor.can().chain().focus().toggleStrike().run(),
                        className: "px-3 py-1 rounded text-sm font-medium ".concat(editor.isActive('strike') ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100', " border border-gray-300"),
                        children: "删除线"
                    }, void 0, false, {
                        fileName: "[project]/src/components/RichTextEditor.tsx",
                        lineNumber: 168,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-px h-6 bg-gray-300 mx-1"
                    }, void 0, false, {
                        fileName: "[project]/src/components/RichTextEditor.tsx",
                        lineNumber: 179,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>editor.chain().focus().toggleHeading({
                                level: 1
                            }).run(),
                        className: "px-3 py-1 rounded text-sm font-medium ".concat(editor.isActive('heading', {
                            level: 1
                        }) ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100', " border border-gray-300"),
                        children: "H1"
                    }, void 0, false, {
                        fileName: "[project]/src/components/RichTextEditor.tsx",
                        lineNumber: 180,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>editor.chain().focus().toggleHeading({
                                level: 2
                            }).run(),
                        className: "px-3 py-1 rounded text-sm font-medium ".concat(editor.isActive('heading', {
                            level: 2
                        }) ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100', " border border-gray-300"),
                        children: "H2"
                    }, void 0, false, {
                        fileName: "[project]/src/components/RichTextEditor.tsx",
                        lineNumber: 190,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>editor.chain().focus().toggleHeading({
                                level: 3
                            }).run(),
                        className: "px-3 py-1 rounded text-sm font-medium ".concat(editor.isActive('heading', {
                            level: 3
                        }) ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100', " border border-gray-300"),
                        children: "H3"
                    }, void 0, false, {
                        fileName: "[project]/src/components/RichTextEditor.tsx",
                        lineNumber: 200,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-px h-6 bg-gray-300 mx-1"
                    }, void 0, false, {
                        fileName: "[project]/src/components/RichTextEditor.tsx",
                        lineNumber: 210,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>editor.chain().focus().toggleBulletList().run(),
                        className: "px-3 py-1 rounded text-sm font-medium ".concat(editor.isActive('bulletList') ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100', " border border-gray-300"),
                        children: "无序列表"
                    }, void 0, false, {
                        fileName: "[project]/src/components/RichTextEditor.tsx",
                        lineNumber: 211,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>editor.chain().focus().toggleOrderedList().run(),
                        className: "px-3 py-1 rounded text-sm font-medium ".concat(editor.isActive('orderedList') ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100', " border border-gray-300"),
                        children: "有序列表"
                    }, void 0, false, {
                        fileName: "[project]/src/components/RichTextEditor.tsx",
                        lineNumber: 221,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-px h-6 bg-gray-300 mx-1"
                    }, void 0, false, {
                        fileName: "[project]/src/components/RichTextEditor.tsx",
                        lineNumber: 231,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: setLink,
                        className: "px-3 py-1 rounded text-sm font-medium ".concat(editor.isActive('link') ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100', " border border-gray-300"),
                        children: "链接"
                    }, void 0, false, {
                        fileName: "[project]/src/components/RichTextEditor.tsx",
                        lineNumber: 232,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: addImage,
                        className: "px-3 py-1 rounded text-sm font-medium bg-white text-gray-700 hover:bg-gray-100 border border-gray-300",
                        children: "插入链接图片"
                    }, void 0, false, {
                        fileName: "[project]/src/components/RichTextEditor.tsx",
                        lineNumber: 242,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: uploadImage,
                        className: "px-3 py-1 rounded text-sm font-medium bg-white text-gray-700 hover:bg-gray-100 border border-gray-300",
                        children: "上传图片"
                    }, void 0, false, {
                        fileName: "[project]/src/components/RichTextEditor.tsx",
                        lineNumber: 248,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-px h-6 bg-gray-300 mx-1"
                    }, void 0, false, {
                        fileName: "[project]/src/components/RichTextEditor.tsx",
                        lineNumber: 254,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>editor.chain().focus().toggleBlockquote().run(),
                        className: "px-3 py-1 rounded text-sm font-medium ".concat(editor.isActive('blockquote') ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100', " border border-gray-300"),
                        children: "引用"
                    }, void 0, false, {
                        fileName: "[project]/src/components/RichTextEditor.tsx",
                        lineNumber: 255,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>editor.chain().focus().setHorizontalRule().run(),
                        className: "px-3 py-1 rounded text-sm font-medium bg-white text-gray-700 hover:bg-gray-100 border border-gray-300",
                        children: "分割线"
                    }, void 0, false, {
                        fileName: "[project]/src/components/RichTextEditor.tsx",
                        lineNumber: 265,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-px h-6 bg-gray-300 mx-1"
                    }, void 0, false, {
                        fileName: "[project]/src/components/RichTextEditor.tsx",
                        lineNumber: 271,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>editor.chain().focus().undo().run(),
                        disabled: !editor.can().chain().focus().undo().run(),
                        className: "px-3 py-1 rounded text-sm font-medium bg-white text-gray-700 hover:bg-gray-100 border border-gray-300 disabled:opacity-50",
                        children: "撤销"
                    }, void 0, false, {
                        fileName: "[project]/src/components/RichTextEditor.tsx",
                        lineNumber: 272,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>editor.chain().focus().redo().run(),
                        disabled: !editor.can().chain().focus().redo().run(),
                        className: "px-3 py-1 rounded text-sm font-medium bg-white text-gray-700 hover:bg-gray-100 border border-gray-300 disabled:opacity-50",
                        children: "重做"
                    }, void 0, false, {
                        fileName: "[project]/src/components/RichTextEditor.tsx",
                        lineNumber: 279,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/RichTextEditor.tsx",
                lineNumber: 145,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$react$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["EditorContent"], {
                    editor: editor
                }, void 0, false, {
                    fileName: "[project]/src/components/RichTextEditor.tsx",
                    lineNumber: 290,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/RichTextEditor.tsx",
                lineNumber: 289,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/RichTextEditor.tsx",
        lineNumber: 143,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(RichTextEditor, "MhgbVgjCt3iJ/JJrsGBPSj5zPHU=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$react$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useEditor"]
    ];
});
_c = RichTextEditor;
const __TURBOPACK__default__export__ = RichTextEditor;
var _c;
__turbopack_context__.k.register(_c, "RichTextEditor");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>Home
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$RichTextEditor$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/RichTextEditor.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function Home() {
    _s();
    const [url, setUrl] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [content, setContent] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [richTextContent, setRichTextContent] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [extractedData, setExtractedData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [activeTab, setActiveTab] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('url');
    const [selectedStyle, setSelectedStyle] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('classic');
    const [formattedContent, setFormattedContent] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [availableStyles, setAvailableStyles] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [originalityResult, setOriginalityResult] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isCheckingOriginality, setIsCheckingOriginality] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isPreviewMode, setIsPreviewMode] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // 获取可用的排版样式
    const fetchAvailableStyles = async ()=>{
        try {
            const response = await fetch('/api/format');
            if (response.ok) {
                const data = await response.json();
                setAvailableStyles(data.styles);
            }
        } catch (error) {
            console.error('获取样式失败:', error);
        }
    };
    // 组件加载时获取样式
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useEffect({
        "Home.useEffect": ()=>{
            fetchAvailableStyles();
        }
    }["Home.useEffect"], []);
    // 处理图片URL，使用代理解决跨域问题
    const processImageUrl = (url)=>{
        if (!url) return '';
        // 如果是相对路径或协议相对路径，跳过
        if (url.startsWith('/') && !url.startsWith('//')) {
            return url;
        }
        // 如果是协议相对路径，添加https
        if (url.startsWith('//')) {
            url = 'https:' + url;
        }
        // 使用代理URL来避免跨域问题
        return "/api/image-proxy?url=".concat(encodeURIComponent(url));
    };
    // 生成兼容性更好的富文本内容（用于复制到其他编辑器）
    const convertToCompatibleRichText = (data)=>{
        let richContent = '\n      <table style="width: 100%; max-width: 800px; margin: 0 auto; font-family: \'Microsoft YaHei\', \'PingFang SC\', \'Hiragino Sans GB\', sans-serif; line-height: 1.8; color: #333; border-collapse: collapse;">\n        <tr>\n          <td style="padding: 0;">\n            <!-- 标题区域 -->\n            <table style="width: 100%; margin-bottom: 30px; border-collapse: collapse;">\n              <tr>\n                <td style="text-align: center;">\n                  <h1 style="\n                    font-size: 28px;\n                    font-weight: bold;\n                    margin: 20px 0 10px 0;\n                    padding: 15px 25px;\n                    background: #f0f8ff;\n                    color: #2c3e50;\n                    border: 3px solid #3498db;\n                    display: inline-block;\n                  ">'.concat(data.title || '文章标题', "</h1>");
        // 副标题
        if (data.subtitle) {
            richContent += '\n                  <h2 style="\n                    font-size: 20px;\n                    font-weight: bold;\n                    color: #34495e;\n                    margin: 15px 0 5px 0;\n                    text-align: center;\n                  ">'.concat(data.subtitle, "</h2>");
        }
        // 作者信息
        if (data.author) {
            richContent += '\n                  <p style="\n                    text-align: center;\n                    font-size: 16px;\n                    color: #7f8c8d;\n                    margin: 10px 0 20px 0;\n                  ">作者：'.concat(data.author, "</p>");
        }
        richContent += "\n                </td>\n              </tr>\n            </table>";
        // 摘要
        if (data.summary && data.hasSummary) {
            richContent += '\n            <table style="width: 100%; background: #fff3cd; margin: 25px 0; border: 2px solid #ffc107; border-collapse: collapse;">\n              <tr>\n                <td style="padding: 20px;">\n                  <h3 style="\n                    font-size: 18px;\n                    font-weight: bold;\n                    color: #856404;\n                    margin: 0 0 10px 0;\n                    border-bottom: 2px solid #ffc107;\n                    padding-bottom: 5px;\n                    display: inline-block;\n                  ">摘要</h3>\n                  <p style="\n                    margin: 0;\n                    font-size: 15px;\n                    color: #856404;\n                    line-height: 1.7;\n                    text-indent: 0;\n                  ">'.concat(data.summary, "</p>\n                </td>\n              </tr>\n            </table>");
        }
        // 处理正文内容
        let contentText = '';
        if (data.content && data.content.includes('<')) {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = data.content;
            const paragraphs = Array.from(tempDiv.querySelectorAll('p')).map((p)=>{
                var _p_textContent;
                return (_p_textContent = p.textContent) === null || _p_textContent === void 0 ? void 0 : _p_textContent.trim();
            }).filter(Boolean);
            contentText = paragraphs.join('\n\n');
        } else {
            contentText = data.content || '';
        }
        // 检查并移除已存在的版权声明，避免重复
        contentText = contentText.replace(/本网站作品著作权归作者本人所有，凡发表在网站的文章，未经作者本人授权，不得转载。/g, '').trim();
        // 正文内容区域
        richContent += '\n            <table style="width: 100%; background: #f8f9fa; margin: 25px 0; border: 2px solid #3498db; border-collapse: collapse;">\n              <tr>\n                <td style="padding: 25px;">';
        // 处理正文段落和图片
        const paragraphs = contentText.split('\n').filter((p)=>p.trim());
        const images = data.images || [];
        if (images.length > 0) {
            const imagePositions = [];
            const interval = Math.max(1, Math.floor(paragraphs.length / images.length));
            for(let i = 0; i < images.length; i++){
                imagePositions.push(Math.min(paragraphs.length, (i + 1) * interval));
            }
            let imageIndex = 0;
            for(let i = 0; i <= paragraphs.length; i++){
                if (imagePositions.includes(i) && imageIndex < images.length) {
                    const proxiedImg = processImageUrl(images[imageIndex]);
                    richContent += '\n                  <table style="width: 100%; margin: 25px 0; border-collapse: collapse;">\n                    <tr>\n                      <td style="text-align: center;">\n                        <img src="'.concat(proxiedImg, '" alt="图片 ').concat(imageIndex + 1, '" style="\n                          max-width: 100%;\n                          height: auto;\n                          border: 2px solid #3498db;\n                        " />\n                      </td>\n                    </tr>\n                  </table>');
                    imageIndex++;
                }
                if (i < paragraphs.length) {
                    richContent += '\n                  <p style="\n                    margin: 15px 0;\n                    font-size: 16px;\n                    line-height: 1.8;\n                    color: #333;\n                    text-indent: 0;\n                  ">'.concat(paragraphs[i].trim(), "</p>");
                }
            }
            while(imageIndex < images.length){
                const proxiedImg = processImageUrl(images[imageIndex]);
                richContent += '\n                  <table style="width: 100%; margin: 25px 0; border-collapse: collapse;">\n                    <tr>\n                      <td style="text-align: center;">\n                        <img src="'.concat(proxiedImg, '" alt="图片 ').concat(imageIndex + 1, '" style="\n                          max-width: 100%;\n                          height: auto;\n                          border: 2px solid #3498db;\n                        " />\n                      </td>\n                    </tr>\n                  </table>');
                imageIndex++;
            }
        } else {
            paragraphs.forEach((paragraph)=>{
                richContent += '\n                  <p style="\n                    margin: 15px 0;\n                    font-size: 16px;\n                    line-height: 1.8;\n                    color: #333;\n                    text-indent: 0;\n                  ">'.concat(paragraph.trim(), "</p>");
            });
        }
        // 版权声明
        richContent += '\n                  <p style="\n                    margin: 30px 0 15px 0;\n                    font-size: 14px;\n                    color: #999;\n                    text-align: left;\n                    text-indent: 0;\n                    border-top: 1px solid #eee;\n                    padding-top: 15px;\n                  ">本网站作品著作权归作者本人所有，凡发表在网站的文章，未经作者本人授权，不得转载。</p>\n                </td>\n              </tr>\n            </table>';
        // 编者按和作者简介区域
        if (data.editorNote || data.authorBio) {
            richContent += '\n            <table style="width: 100%; background: #fff; margin: 25px 0; border: 2px solid #e9ecef; border-collapse: collapse;">\n              <tr>\n                <td style="padding: 25px;">';
            if (data.editorNote) {
                let cleanEditorNote = data.editorNote;
                cleanEditorNote = cleanEditorNote.replace(/^【?编者按】?[：:]?\s*/i, '');
                cleanEditorNote = cleanEditorNote.replace(/^编者按[：:]?\s*/i, '');
                richContent += '\n                  <h3 style="\n                    font-size: 18px;\n                    font-weight: bold;\n                    color: #333;\n                    margin: 0 0 10px 0;\n                    border-bottom: 2px solid #3498db;\n                    padding-bottom: 5px;\n                    display: inline-block;\n                  ">编者按</h3>\n                  <p style="\n                    margin: 0 0 20px 0;\n                    font-size: 15px;\n                    color: #555;\n                    line-height: 1.7;\n                    text-indent: 0;\n                  ">'.concat(cleanEditorNote, "</p>");
            }
            if (data.authorBio) {
                let cleanAuthorBio = data.authorBio;
                cleanAuthorBio = cleanAuthorBio.replace(/^作者[：:]?\s*/i, '');
                richContent += '\n                  <h3 style="\n                    font-size: 18px;\n                    font-weight: bold;\n                    color: #333;\n                    margin: 0 0 15px 0;\n                    border-bottom: 2px solid #3498db;\n                    padding-bottom: 5px;\n                    display: inline-block;\n                  ">作者简介</h3>\n                  <table style="width: 100%; border-collapse: collapse;">\n                    <tr>';
                if (data.authorPhoto) {
                    const proxiedPhoto = processImageUrl(data.authorPhoto);
                    richContent += '\n                      <td style="width: 80px; vertical-align: top; padding-right: 15px;">\n                        <img src="'.concat(proxiedPhoto, '" alt="').concat(data.author, '" style="\n                          width: 80px;\n                          height: 80px;\n                          border: 3px solid #3498db;\n                        " />\n                      </td>');
                }
                richContent += '\n                      <td style="vertical-align: top;">\n                        <p style="\n                          margin: 0 0 5px 0;\n                          font-size: 16px;\n                          font-weight: bold;\n                          color: #333;\n                        ">'.concat(data.author, '</p>\n                        <p style="\n                          margin: 0;\n                          font-size: 15px;\n                          color: #555;\n                          line-height: 1.7;\n                          text-indent: 0;\n                        ">').concat(cleanAuthorBio, "</p>\n                      </td>\n                    </tr>\n                  </table>");
            }
            richContent += "\n                </td>\n              </tr>\n            </table>";
        }
        richContent += "\n          </td>\n        </tr>\n      </table>";
        return richContent;
    };
    // 生成随机的文章样式
    const generateArticleStyle = ()=>{
        const styles = [
            {
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                textColor: 'white',
                borderColor: '#667eea'
            },
            {
                background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                textColor: 'white',
                borderColor: '#f093fb'
            },
            {
                background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                textColor: 'white',
                borderColor: '#4facfe'
            },
            {
                background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                textColor: 'white',
                borderColor: '#43e97b'
            },
            {
                background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
                textColor: 'white',
                borderColor: '#fa709a'
            }
        ];
        return styles[Math.floor(Math.random() * styles.length)];
    };
    // 将提取的内容转换为富文本格式
    const convertToRichText = (data)=>{
        const style = generateArticleStyle();
        let richContent = "\n      <div style=\"max-width: 800px; margin: 0 auto; font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif; line-height: 1.8; color: #333;\">\n        <!-- 标题区域 -->\n        <div style=\"text-align: center; margin-bottom: 30px;\">\n          <h1 style=\"\n            font-size: 28px;\n            font-weight: bold;\n            margin: 20px 0 10px 0;\n            padding: 15px 25px;\n            background: ".concat(style.background, ";\n            color: ").concat(style.textColor, ";\n            border: 3px solid ").concat(style.borderColor, ';\n            display: inline-block;\n          ">').concat(data.title || '文章标题', "</h1>");
        if (data.subtitle) {
            richContent += '\n          <h2 style="\n            font-size: 20px;\n            font-weight: 500;\n            margin: 15px 0 10px 0;\n            color: #666;\n          ">'.concat(data.subtitle, "</h2>");
        }
        richContent += '\n          <p style="\n            font-size: 16px;\n            color: #888;\n            margin: 10px 0 0 0;\n            font-weight: 500;\n          ">作者：'.concat(data.author || '未知作者', "</p>\n        </div>");
        // 摘要 - 只有当真正存在article-abs且有摘要内容时才显示
        if (data.hasSummary && data.summary && data.summary.trim()) {
            // 清理摘要内容，去掉重复的"摘要："
            let cleanSummary = data.summary.trim();
            cleanSummary = cleanSummary.replace(/^摘要[：:]?\s*/i, '');
            // 如果清理后还有内容，才显示摘要区域
            if (cleanSummary.trim()) {
                richContent += '\n        <div style="\n          background: #f8f9fa;\n          padding: 20px;\n          margin: 25px 0;\n          border-left: 4px solid '.concat(style.borderColor, ';\n          border-radius: 0 8px 8px 0;\n          box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n        ">\n          <p style="\n            margin: 0;\n            font-size: 16px;\n            color: #555;\n            font-style: italic;\n            text-indent: 0;\n          "><strong>摘要：</strong>').concat(cleanSummary, "</p>\n        </div>");
            }
        }
        // 正文内容区域开始
        richContent += '\n        <div style="\n          background: #f8f9fa;\n          padding: 25px;\n          margin: 25px 0;\n          border: 2px solid '.concat(style.borderColor, ';\n        ">');
        // 处理正文内容
        let contentText = '';
        if (data.content && data.content.includes('<')) {
            // 如果是HTML格式，清理并提取文本
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = data.content;
            const paragraphs = Array.from(tempDiv.querySelectorAll('p')).map((p)=>{
                var _p_textContent;
                return (_p_textContent = p.textContent) === null || _p_textContent === void 0 ? void 0 : _p_textContent.trim();
            }).filter(Boolean);
            contentText = paragraphs.join('\n\n');
        } else {
            contentText = data.content || '';
        }
        // 检查并移除已存在的版权声明，避免重复
        contentText = contentText.replace(/本网站作品著作权归作者本人所有，凡发表在网站的文章，未经作者本人授权，不得转载。/g, '').trim();
        // 处理正文段落和图片的均匀分布
        const paragraphs = contentText.split('\n').filter((p)=>p.trim());
        const images = data.images || [];
        if (images.length > 0 && paragraphs.length > 0) {
            // 计算图片插入位置，让图片均匀分布在段落之间
            const totalSlots = paragraphs.length + 1; // 段落前后都可以插入图片
            const imagePositions = [];
            if (images.length >= totalSlots) {
                // 图片数量多于或等于可插入位置，每个位置都插入
                for(let i = 0; i < totalSlots; i++){
                    imagePositions.push(i);
                }
            } else {
                // 图片数量少于可插入位置，均匀分布
                const step = totalSlots / images.length;
                for(let i = 0; i < images.length; i++){
                    const position = Math.floor(i * step);
                    imagePositions.push(position);
                }
            }
            // 构建内容，交替插入段落和图片
            let imageIndex = 0;
            for(let i = 0; i <= paragraphs.length; i++){
                // 检查是否需要在这个位置插入图片
                if (imagePositions.includes(i) && imageIndex < images.length) {
                    const proxiedImg = processImageUrl(images[imageIndex]);
                    richContent += '\n          <div style="text-align: center; margin: 25px 0;">\n            <img src="'.concat(proxiedImg, '" alt="图片 ').concat(imageIndex + 1, '" style="\n              max-width: 100%;\n              height: auto;\n              border: 2px solid ').concat(style.borderColor, ';\n            " />\n          </div>');
                    imageIndex++;
                }
                // 插入段落（如果不是最后一个位置）
                if (i < paragraphs.length) {
                    richContent += '\n          <p style="\n            margin: 15px 0;\n            font-size: 16px;\n            line-height: 1.8;\n            color: #333;\n            text-indent: 0;\n          ">'.concat(paragraphs[i].trim(), "</p>");
                }
            }
            // 如果还有剩余图片，添加到最后
            while(imageIndex < images.length){
                const proxiedImg = processImageUrl(images[imageIndex]);
                richContent += '\n          <div style="text-align: center; margin: 25px 0;">\n            <img src="'.concat(proxiedImg, '" alt="图片 ').concat(imageIndex + 1, '" style="\n              max-width: 100%;\n              height: auto;\n              border: 2px solid ').concat(style.borderColor, ';\n            " />\n          </div>');
                imageIndex++;
            }
        } else {
            // 没有图片时，只添加段落
            paragraphs.forEach((paragraph)=>{
                richContent += '\n          <p style="\n            margin: 15px 0;\n            font-size: 16px;\n            line-height: 1.8;\n            color: #333;\n            text-indent: 0;\n          ">'.concat(paragraph.trim(), "</p>");
            });
        }
        // 版权声明
        richContent += '\n          <p style="\n            margin: 30px 0 15px 0;\n            font-size: 14px;\n            color: #999;\n            text-align: left;\n            text-indent: 0;\n            border-top: 1px solid #eee;\n            padding-top: 15px;\n          ">本网站作品著作权归作者本人所有，凡发表在网站的文章，未经作者本人授权，不得转载。</p>\n        </div>';
        // 编者按和作者简介区域
        if (data.editorNote || data.authorBio) {
            richContent += '\n        <div style="\n          background: #fff;\n          padding: 25px;\n          margin: 25px 0;\n          border-radius: 12px;\n          border: 2px solid #e9ecef;\n          box-shadow: 0 4px 15px rgba(0,0,0,0.1);\n        ">';
            // 编者按
            if (data.editorNote) {
                // 清理编者按内容，去掉重复的标题
                let cleanEditorNote = data.editorNote;
                cleanEditorNote = cleanEditorNote.replace(/^【?编者按】?[：:]?\s*/i, '');
                cleanEditorNote = cleanEditorNote.replace(/^编者按[：:]?\s*/i, '');
                richContent += '\n          <div style="margin-bottom: 20px;">\n            <h3 style="\n              font-size: 18px;\n              font-weight: bold;\n              color: #333;\n              margin: 0 0 10px 0;\n              border-bottom: 2px solid '.concat(style.borderColor, ';\n              padding-bottom: 5px;\n              display: inline-block;\n            ">编者按</h3>\n            <p style="\n              margin: 0;\n              font-size: 15px;\n              color: #555;\n              line-height: 1.7;\n              text-indent: 0;\n            ">').concat(cleanEditorNote, "</p>\n          </div>");
            }
            // 作者简介
            if (data.authorBio) {
                // 清理作者简介内容，去掉重复的"作者："
                let cleanAuthorBio = data.authorBio;
                cleanAuthorBio = cleanAuthorBio.replace(/^作者[：:]?\s*/i, '');
                richContent += '\n          <div>\n            <h3 style="\n              font-size: 18px;\n              font-weight: bold;\n              color: #333;\n              margin: 0 0 15px 0;\n              border-bottom: 2px solid '.concat(style.borderColor, ';\n              padding-bottom: 5px;\n              display: inline-block;\n            ">作者简介</h3>\n            <div style="display: flex; align-items: flex-start; gap: 15px;">');
                if (data.authorPhoto) {
                    const proxiedPhoto = processImageUrl(data.authorPhoto);
                    richContent += '\n              <img src="'.concat(proxiedPhoto, '" alt="').concat(data.author, '" style="\n                width: 80px;\n                height: 80px;\n                border-radius: 50%;\n                object-fit: cover;\n                border: 3px solid ').concat(style.borderColor, ';\n                flex-shrink: 0;\n              " />');
                }
                richContent += '\n              <div>\n                <p style="\n                  margin: 0 0 5px 0;\n                  font-size: 16px;\n                  font-weight: bold;\n                  color: #333;\n                ">'.concat(data.author, '</p>\n                <p style="\n                  margin: 0;\n                  font-size: 15px;\n                  color: #555;\n                  line-height: 1.7;\n                  text-indent: 0;\n                ">').concat(cleanAuthorBio, "</p>\n              </div>\n            </div>\n          </div>");
            }
            richContent += "</div>";
        }
        richContent += "</div>";
        return richContent;
    };
    const handleUrlExtract = async ()=>{
        if (!url) return;
        setIsLoading(true);
        try {
            const response = await fetch('/api/extract', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    url
                })
            });
            if (!response.ok) {
                throw new Error('提取失败');
            }
            const data = await response.json();
            // 转换内容为富文本格式
            const richContent = convertToRichText(data);
            const processedData = {
                ...data,
                content: richContent
            };
            setExtractedData(processedData);
        } catch (error) {
            console.error('提取失败:', error);
            alert('提取失败，请检查URL是否有效');
        } finally{
            setIsLoading(false);
        }
    };
    const handleContentPaste = ()=>{
        if (!content) return;
        // 将粘贴的内容转换为结构化数据
        const lines = content.split('\n').filter((line)=>line.trim());
        const title = lines[0] || '粘贴的文章';
        const contentText = lines.slice(1).join('\n');
        const pastedData = {
            title,
            author: '用户输入',
            summary: contentText.substring(0, 200) + '...',
            content: contentText,
            images: [],
            editorNote: ''
        };
        // 转换为富文本格式
        const richContent = convertToRichText(pastedData);
        const processedData = {
            ...pastedData,
            content: richContent
        };
        setExtractedData(processedData);
    };
    const handleAutoFormat = async ()=>{
        if (!(extractedData === null || extractedData === void 0 ? void 0 : extractedData.content)) {
            alert('请先提取或输入内容');
            return;
        }
        setIsLoading(true);
        try {
            const response = await fetch('/api/format', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    content: extractedData.content,
                    styleId: selectedStyle
                })
            });
            if (!response.ok) {
                throw new Error('格式化失败');
            }
            const data = await response.json();
            setFormattedContent(data.formattedContent);
            // 更新提取数据中的内容为格式化后的内容
            setExtractedData({
                ...extractedData,
                content: data.formattedContent
            });
            alert("已应用 ".concat(data.styleName, " 样式"));
        } catch (error) {
            console.error('自动排版失败:', error);
            alert('自动排版失败，请重试');
        } finally{
            setIsLoading(false);
        }
    };
    const handleOriginalityCheck = async ()=>{
        if (!(extractedData === null || extractedData === void 0 ? void 0 : extractedData.content)) {
            alert('请先提取或输入内容');
            return;
        }
        setIsCheckingOriginality(true);
        try {
            const response = await fetch('/api/originality', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    content: extractedData.content,
                    title: extractedData.title || ''
                })
            });
            if (!response.ok) {
                throw new Error('原创检测失败');
            }
            const result = await response.json();
            setOriginalityResult(result);
        } catch (error) {
            console.error('原创检测失败:', error);
            alert('原创检测失败，请重试');
        } finally{
            setIsCheckingOriginality(false);
        }
    };
    const handlePublishToWechat = ()=>{
        // TODO: 实现微信公众号发布
        console.log('发布到微信公众号');
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gray-50 py-8",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-6xl mx-auto px-4",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                    className: "text-center mb-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-4xl font-bold text-gray-900 mb-2",
                            children: "微信公众号自动排版工具"
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 792,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-lg text-gray-600",
                            children: "智能提取、自动排版、原创检测、一键发布"
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 795,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 791,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "grid grid-cols-1 lg:grid-cols-5 gap-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "lg:col-span-3 space-y-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-white rounded-lg shadow-md overflow-hidden",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex border-b border-gray-200",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: ()=>setActiveTab('url'),
                                                    className: "flex-1 px-4 py-3 text-sm font-medium ".concat(activeTab === 'url' ? 'bg-blue-600 text-white' : 'bg-gray-50 text-gray-700 hover:bg-gray-100'),
                                                    children: "URL导入"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 806,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: ()=>setActiveTab('paste'),
                                                    className: "flex-1 px-4 py-3 text-sm font-medium ".concat(activeTab === 'paste' ? 'bg-blue-600 text-white' : 'bg-gray-50 text-gray-700 hover:bg-gray-100'),
                                                    children: "文本粘贴"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 816,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: ()=>setActiveTab('editor'),
                                                    className: "flex-1 px-4 py-3 text-sm font-medium ".concat(activeTab === 'editor' ? 'bg-blue-600 text-white' : 'bg-gray-50 text-gray-700 hover:bg-gray-100'),
                                                    children: "富文本编辑"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 826,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 805,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "p-6",
                                            children: [
                                                activeTab === 'url' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "space-y-4",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                            type: "url",
                                                            value: url,
                                                            onChange: (e)=>setUrl(e.target.value),
                                                            placeholder: "请输入文章URL",
                                                            className: "w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 842,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            onClick: handleUrlExtract,
                                                            disabled: !url || isLoading,
                                                            className: "w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed",
                                                            children: isLoading ? '提取中...' : '提取内容'
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 849,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 841,
                                                    columnNumber: 19
                                                }, this),
                                                activeTab === 'paste' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "space-y-4",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                                            value: content,
                                                            onChange: (e)=>setContent(e.target.value),
                                                            placeholder: "请粘贴文章内容",
                                                            rows: 8,
                                                            className: "w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 862,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            onClick: handleContentPaste,
                                                            disabled: !content,
                                                            className: "w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed",
                                                            children: "处理内容"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 869,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 861,
                                                    columnNumber: 19
                                                }, this),
                                                activeTab === 'editor' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "space-y-4",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$RichTextEditor$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            content: richTextContent,
                                                            onChange: setRichTextContent,
                                                            placeholder: "请输入或粘贴文章内容（支持图片、链接等富文本格式）"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 882,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            onClick: ()=>{
                                                                // 将富文本内容转换为提取数据格式
                                                                const tempDiv = document.createElement('div');
                                                                tempDiv.innerHTML = richTextContent;
                                                                const textContent = tempDiv.textContent || tempDiv.innerText || '';
                                                                setExtractedData({
                                                                    title: '富文本编辑内容',
                                                                    author: '用户输入',
                                                                    summary: textContent.substring(0, 200) + '...',
                                                                    content: richTextContent,
                                                                    images: [],
                                                                    editorNote: ''
                                                                });
                                                            },
                                                            disabled: !richTextContent,
                                                            className: "w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed",
                                                            children: "使用富文本内容"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 887,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 881,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 838,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 804,
                                    columnNumber: 13
                                }, this),
                                extractedData && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-white rounded-lg shadow-md p-6",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex justify-between items-center mb-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                    className: "text-xl font-semibold",
                                                    children: "文章编辑器"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 917,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center space-x-4",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex space-x-2 text-sm text-gray-600",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    children: [
                                                                        "作者：",
                                                                        extractedData.author
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/page.tsx",
                                                                    lineNumber: 920,
                                                                    columnNumber: 23
                                                                }, this),
                                                                extractedData.images && extractedData.images.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    children: [
                                                                        "图片：",
                                                                        extractedData.images.length,
                                                                        "张"
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/page.tsx",
                                                                    lineNumber: 922,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 919,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex bg-gray-100 rounded-lg p-1",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                    onClick: ()=>setIsPreviewMode(false),
                                                                    className: "px-3 py-1 rounded-md text-sm font-medium transition-colors ".concat(!isPreviewMode ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),
                                                                    children: "编辑"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/page.tsx",
                                                                    lineNumber: 926,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                    onClick: ()=>setIsPreviewMode(true),
                                                                    className: "px-3 py-1 rounded-md text-sm font-medium transition-colors ".concat(isPreviewMode ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),
                                                                    children: "预览"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/page.tsx",
                                                                    lineNumber: 936,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 925,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 918,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 916,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: isPreviewMode ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "border border-gray-200 rounded-lg bg-white",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "bg-gray-50 px-4 py-2 border-b border-gray-200 rounded-t-lg",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "text-sm font-medium text-gray-700",
                                                            children: "预览模式"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 955,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 954,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "prose prose-lg max-w-none p-6 min-h-[500px]",
                                                        style: {
                                                            fontFamily: '"PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif',
                                                            lineHeight: '1.8',
                                                            color: '#333'
                                                        },
                                                        dangerouslySetInnerHTML: {
                                                            __html: extractedData.content
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 957,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 953,
                                                columnNumber: 21
                                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$RichTextEditor$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                content: extractedData.content,
                                                onChange: (content)=>setExtractedData({
                                                        ...extractedData,
                                                        content
                                                    }),
                                                placeholder: "请输入完整的文章内容，包括标题、副标题、作者、摘要、正文、编者按等..."
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 968,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 951,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 915,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 802,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "lg:col-span-2 space-y-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-white rounded-lg shadow-md p-6",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            className: "text-xl font-semibold mb-4",
                                            children: "排版样式"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 983,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-3",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                                    value: selectedStyle,
                                                    onChange: (e)=>setSelectedStyle(e.target.value),
                                                    className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                                                    children: availableStyles.map((style)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                            value: style.id,
                                                            children: [
                                                                style.name,
                                                                " - ",
                                                                style.description
                                                            ]
                                                        }, style.id, true, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 991,
                                                            columnNumber: 21
                                                        }, this))
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 985,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: handleAutoFormat,
                                                    disabled: !(extractedData === null || extractedData === void 0 ? void 0 : extractedData.content) || isLoading,
                                                    className: "w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed",
                                                    children: isLoading ? '排版中...' : '应用排版样式'
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 996,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 984,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 982,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-white rounded-lg shadow-md p-6",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            className: "text-xl font-semibold mb-4",
                                            children: "操作"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 1008,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-3",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: handleOriginalityCheck,
                                                    disabled: !(extractedData === null || extractedData === void 0 ? void 0 : extractedData.content) || isCheckingOriginality,
                                                    className: "w-full bg-orange-600 text-white py-2 px-4 rounded-md hover:bg-orange-700 disabled:bg-gray-400 disabled:cursor-not-allowed",
                                                    children: isCheckingOriginality ? '检测中...' : '原创检测'
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 1010,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: handlePublishToWechat,
                                                    className: "w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700",
                                                    children: "发布到微信公众号"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 1017,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: async ()=>{
                                                        if (extractedData === null || extractedData === void 0 ? void 0 : extractedData.content) {
                                                            try {
                                                                // 创建一个临时的div来处理HTML内容
                                                                const tempDiv = document.createElement('div');
                                                                tempDiv.innerHTML = extractedData.content;
                                                                // 处理图片：将代理URL转换回原始URL
                                                                const images = tempDiv.querySelectorAll('img');
                                                                images.forEach((img)=>{
                                                                    const src = img.src;
                                                                    if (src.includes('/api/image-proxy?url=')) {
                                                                        const originalUrl = decodeURIComponent(src.split('url=')[1]);
                                                                        img.src = originalUrl;
                                                                    }
                                                                });
                                                                // 创建富文本内容用于复制
                                                                const htmlContent = tempDiv.innerHTML;
                                                                const textContent = tempDiv.textContent || tempDiv.innerText || '';
                                                                // 使用现代的Clipboard API复制富文本
                                                                if (navigator.clipboard && window.ClipboardItem) {
                                                                    const clipboardItem = new ClipboardItem({
                                                                        'text/html': new Blob([
                                                                            htmlContent
                                                                        ], {
                                                                            type: 'text/html'
                                                                        }),
                                                                        'text/plain': new Blob([
                                                                            textContent
                                                                        ], {
                                                                            type: 'text/plain'
                                                                        })
                                                                    });
                                                                    await navigator.clipboard.write([
                                                                        clipboardItem
                                                                    ]);
                                                                    alert('富文本内容（包含图片）已复制到剪贴板，可直接粘贴到微信公众号编辑器');
                                                                } else {
                                                                    // 降级方案：只复制文本
                                                                    await navigator.clipboard.writeText(textContent);
                                                                    alert('文本内容已复制到剪贴板');
                                                                }
                                                            } catch (error) {
                                                                console.error('复制失败:', error);
                                                                // 最后的降级方案
                                                                const textContent = extractedData.content.replace(/<[^>]*>/g, '');
                                                                navigator.clipboard.writeText(textContent);
                                                                alert('已复制纯文本内容到剪贴板');
                                                            }
                                                        }
                                                    },
                                                    disabled: !(extractedData === null || extractedData === void 0 ? void 0 : extractedData.content),
                                                    className: "w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed",
                                                    children: "复制富文本内容"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 1023,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: async ()=>{
                                                        if (extractedData === null || extractedData === void 0 ? void 0 : extractedData.content) {
                                                            try {
                                                                // 生成兼容性更好的富文本内容
                                                                const compatibleContent = convertToCompatibleRichText(extractedData);
                                                                // 创建临时div来处理内容
                                                                const tempDiv = document.createElement('div');
                                                                tempDiv.innerHTML = compatibleContent;
                                                                // 创建富文本内容用于复制
                                                                const htmlContent = tempDiv.innerHTML;
                                                                const textContent = tempDiv.textContent || tempDiv.innerText || '';
                                                                // 使用现代的Clipboard API复制富文本
                                                                if (navigator.clipboard && window.ClipboardItem) {
                                                                    const clipboardItem = new ClipboardItem({
                                                                        'text/html': new Blob([
                                                                            htmlContent
                                                                        ], {
                                                                            type: 'text/html'
                                                                        }),
                                                                        'text/plain': new Blob([
                                                                            textContent
                                                                        ], {
                                                                            type: 'text/plain'
                                                                        })
                                                                    });
                                                                    await navigator.clipboard.write([
                                                                        clipboardItem
                                                                    ]);
                                                                    alert('兼容模式富文本内容已复制到剪贴板，可直接粘贴到其他公众号编辑器');
                                                                } else {
                                                                    // 降级方案：只复制文本
                                                                    await navigator.clipboard.writeText(textContent);
                                                                    alert('文本内容已复制到剪贴板');
                                                                }
                                                            } catch (error) {
                                                                console.error('复制失败:', error);
                                                                // 最后的降级方案
                                                                const textContent = extractedData.content.replace(/<[^>]*>/g, '');
                                                                navigator.clipboard.writeText(textContent);
                                                                alert('已复制纯文本内容到剪贴板');
                                                            }
                                                        }
                                                    },
                                                    disabled: !(extractedData === null || extractedData === void 0 ? void 0 : extractedData.content),
                                                    className: "w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed",
                                                    children: "复制兼容模式内容"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 1072,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 1009,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 1007,
                                    columnNumber: 13
                                }, this),
                                originalityResult && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-white rounded-lg shadow-md p-6",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            className: "text-xl font-semibold mb-4",
                                            children: "原创检测结果"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 1120,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "p-4 rounded-lg ".concat(originalityResult.isOriginal ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'),
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center justify-between",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "font-semibold ".concat(originalityResult.isOriginal ? 'text-green-800' : 'text-red-800'),
                                                                children: originalityResult.isOriginal ? '✅ 原创内容' : '❌ 疑似非原创'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/page.tsx",
                                                                lineNumber: 1129,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "px-3 py-1 rounded-full text-sm font-medium ".concat(originalityResult.riskLevel === 'low' ? 'bg-green-100 text-green-800' : originalityResult.riskLevel === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'),
                                                                children: originalityResult.riskLevel === 'low' ? '低风险' : originalityResult.riskLevel === 'medium' ? '中风险' : '高风险'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/page.tsx",
                                                                lineNumber: 1134,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 1128,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 1123,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "grid grid-cols-2 gap-4",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "bg-gray-50 p-3 rounded-lg",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-sm text-gray-600",
                                                                    children: "相似度"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/page.tsx",
                                                                    lineNumber: 1150,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-2xl font-bold text-gray-900",
                                                                    children: [
                                                                        originalityResult.similarity,
                                                                        "%"
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/page.tsx",
                                                                    lineNumber: 1151,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 1149,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "bg-gray-50 p-3 rounded-lg",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-sm text-gray-600",
                                                                    children: "重复文章数"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/page.tsx",
                                                                    lineNumber: 1156,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-2xl font-bold text-gray-900",
                                                                    children: originalityResult.duplicateCount
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/page.tsx",
                                                                    lineNumber: 1157,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 1155,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 1148,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                            className: "font-semibold text-gray-900 mb-2",
                                                            children: "检测建议"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 1165,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                            className: "space-y-1",
                                                            children: originalityResult.suggestions.map((suggestion, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                    className: "text-sm text-gray-700 flex items-start",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: "mr-2",
                                                                            children: "•"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/page.tsx",
                                                                            lineNumber: 1169,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            children: suggestion
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/page.tsx",
                                                                            lineNumber: 1170,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, index, true, {
                                                                    fileName: "[project]/src/app/page.tsx",
                                                                    lineNumber: 1168,
                                                                    columnNumber: 25
                                                                }, this))
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 1166,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 1164,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 1121,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 1119,
                                    columnNumber: 15
                                }, this),
                                formattedContent && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-white rounded-lg shadow-md p-6",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            className: "text-xl font-semibold mb-4",
                                            children: "排版预览"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 1182,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto",
                                            dangerouslySetInnerHTML: {
                                                __html: formattedContent
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 1183,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 1181,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 980,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 800,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/page.tsx",
            lineNumber: 790,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 789,
        columnNumber: 5
    }, this);
}
_s(Home, "zYlYa3R2ZqLb5Z2HDUigPfuNDoc=");
_c = Home;
var _c;
__turbopack_context__.k.register(_c, "Home");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_d3f6430e._.js.map