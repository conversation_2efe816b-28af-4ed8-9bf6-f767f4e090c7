{"name": "wx-article-formatter", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@tailwindcss/typography": "^0.5.16", "@tiptap/extension-color": "^3.0.9", "@tiptap/extension-font-family": "^3.0.9", "@tiptap/extension-image": "^3.0.9", "@tiptap/extension-link": "^3.0.9", "@tiptap/extension-text-align": "^3.0.9", "@tiptap/extension-text-style": "^3.0.9", "@tiptap/extension-underline": "^3.0.9", "@tiptap/pm": "^3.0.9", "@tiptap/react": "^3.0.9", "@tiptap/starter-kit": "^3.0.9", "@types/cheerio": "^0.22.35", "@types/jsdom": "^21.1.7", "axios": "^1.11.0", "cheerio": "^1.1.2", "jsdom": "^26.1.0", "next": "15.4.5", "puppeteer": "^24.15.0", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "typescript": "^5"}}