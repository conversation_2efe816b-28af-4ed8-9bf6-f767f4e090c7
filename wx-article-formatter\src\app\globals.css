@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* 富文本编辑器样式优化 */
.ProseMirror {
  font-family: "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif !important;
  line-height: 1.8 !important;
  color: #333 !important;
}

.ProseMirror h1 {
  font-size: 2rem !important;
  font-weight: bold !important;
  margin: 1.5rem 0 1rem 0 !important;
  color: #1a1a1a !important;
  text-align: center !important;
}

.ProseMirror h2 {
  font-size: 1.5rem !important;
  font-weight: bold !important;
  margin: 1.25rem 0 0.75rem 0 !important;
  color: #2d3748 !important;
}

.ProseMirror h3 {
  font-size: 1.25rem !important;
  font-weight: bold !important;
  margin: 1rem 0 0.5rem 0 !important;
  color: #4a5568 !important;
}

.ProseMirror p {
  margin: 0.75rem 0 !important;
  text-indent: 2em !important;
  font-size: 16px !important;
  line-height: 1.8 !important;
}

.ProseMirror img {
  max-width: 100% !important;
  height: auto !important;
  margin: 1.5rem auto !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  display: block !important;
}

.ProseMirror blockquote {
  border-left: 4px solid #3182ce !important;
  background: #f7fafc !important;
  padding: 1rem 1.5rem !important;
  margin: 1.5rem 0 !important;
  font-style: italic !important;
  color: #4a5568 !important;
}

.ProseMirror ul, .ProseMirror ol {
  margin: 1rem 0 !important;
  padding-left: 2rem !important;
}

.ProseMirror li {
  margin: 0.5rem 0 !important;
  line-height: 1.6 !important;
}

.ProseMirror strong {
  font-weight: bold !important;
  color: #2d3748 !important;
}

.ProseMirror em {
  font-style: italic !important;
  color: #4a5568 !important;
}

.ProseMirror a {
  color: #3182ce !important;
  text-decoration: underline !important;
}

.ProseMirror hr {
  border: none !important;
  height: 2px !important;
  background: linear-gradient(90deg, #e2e8f0, #cbd5e0, #e2e8f0) !important;
  margin: 2rem 0 !important;
}
