import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';

interface OriginalityResult {
  isOriginal: boolean;
  similarity: number;
  duplicateCount: number;
  suggestions: string[];
  riskLevel: 'low' | 'medium' | 'high';
}

// 模拟的文章指纹数据库（实际应用中应该使用真实的数据库）
const articleDatabase: Array<{
  fingerprint: string;
  title: string;
  content: string;
  source: string;
}> = [
  {
    fingerprint: 'sample1',
    title: '示例文章1',
    content: '这是一个示例文章内容，用于测试原创检测功能。',
    source: '示例网站1'
  },
  {
    fingerprint: 'sample2',
    title: '示例文章2',
    content: '另一个示例文章，包含不同的内容和主题。',
    source: '示例网站2'
  }
];

export async function POST(request: NextRequest) {
  try {
    const { content, title = '' } = await request.json();
    
    if (!content) {
      return NextResponse.json({ error: '请提供要检测的内容' }, { status: 400 });
    }

    // 执行原创检测
    const result = await checkOriginality(content, title);

    return NextResponse.json(result);
  } catch (error) {
    console.error('原创检测失败:', error);
    return NextResponse.json(
      { error: '原创检测失败，请重试' }, 
      { status: 500 }
    );
  }
}

// 原创检测核心函数
async function checkOriginality(content: string, title: string): Promise<OriginalityResult> {
  // 1. 生成文章指纹
  const fingerprint = generateFingerprint(content);
  
  // 2. 检查完全重复
  const exactMatch = articleDatabase.find(article => article.fingerprint === fingerprint);
  if (exactMatch) {
    return {
      isOriginal: false,
      similarity: 100,
      duplicateCount: 1,
      suggestions: [
        '检测到完全相同的文章',
        `来源：${exactMatch.source}`,
        '建议重新创作或标注引用来源'
      ],
      riskLevel: 'high'
    };
  }

  // 3. 检查相似度
  const similarities = articleDatabase.map(article => ({
    article,
    similarity: calculateSimilarity(content, article.content)
  }));

  const maxSimilarity = Math.max(...similarities.map(s => s.similarity));
  const highSimilarityCount = similarities.filter(s => s.similarity > 70).length;

  // 4. 基于关键词的检测
  const keywordSimilarity = checkKeywordSimilarity(content, title);

  // 5. 综合评估
  const finalSimilarity = Math.max(maxSimilarity, keywordSimilarity);
  const isOriginal = finalSimilarity < 30 && highSimilarityCount === 0;
  
  let riskLevel: 'low' | 'medium' | 'high' = 'low';
  if (finalSimilarity > 70) riskLevel = 'high';
  else if (finalSimilarity > 40) riskLevel = 'medium';

  const suggestions = generateSuggestions(finalSimilarity, highSimilarityCount, similarities);

  return {
    isOriginal,
    similarity: Math.round(finalSimilarity),
    duplicateCount: highSimilarityCount,
    suggestions,
    riskLevel
  };
}

// 生成文章指纹
function generateFingerprint(content: string): string {
  // 移除HTML标签和特殊字符，只保留文本内容
  const cleanContent = content
    .replace(/<[^>]*>/g, '')
    .replace(/\s+/g, ' ')
    .trim()
    .toLowerCase();
  
  return crypto.createHash('md5').update(cleanContent).digest('hex');
}

// 计算文本相似度（简化版本）
function calculateSimilarity(text1: string, text2: string): number {
  const clean1 = cleanText(text1);
  const clean2 = cleanText(text2);
  
  if (clean1.length === 0 || clean2.length === 0) return 0;
  
  // 使用简单的字符级别相似度计算
  const longer = clean1.length > clean2.length ? clean1 : clean2;
  const shorter = clean1.length > clean2.length ? clean2 : clean1;
  
  if (longer.length === 0) return 100;
  
  const editDistance = levenshteinDistance(longer, shorter);
  return ((longer.length - editDistance) / longer.length) * 100;
}

// 清理文本
function cleanText(text: string): string {
  return text
    .replace(/<[^>]*>/g, '')
    .replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '')
    .toLowerCase();
}

// 计算编辑距离
function levenshteinDistance(str1: string, str2: string): number {
  const matrix = [];
  
  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }
  
  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }
  
  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        );
      }
    }
  }
  
  return matrix[str2.length][str1.length];
}

// 关键词相似度检测
function checkKeywordSimilarity(content: string, title: string): number {
  // 提取关键词（简化版本）
  const keywords = extractKeywords(content + ' ' + title);
  
  // 检查关键词在数据库中的出现频率
  let totalMatches = 0;
  let totalKeywords = keywords.length;
  
  keywords.forEach(keyword => {
    const matches = articleDatabase.filter(article => 
      article.content.includes(keyword) || article.title.includes(keyword)
    ).length;
    totalMatches += matches;
  });
  
  if (totalKeywords === 0) return 0;
  return Math.min((totalMatches / totalKeywords) * 20, 100);
}

// 提取关键词（简化版本）
function extractKeywords(text: string): string[] {
  const cleanText = text
    .replace(/<[^>]*>/g, '')
    .replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, ' ');
  
  const words = cleanText.split(/\s+/).filter(word => word.length > 2);
  
  // 返回出现频率较高的词汇
  const wordCount: { [key: string]: number } = {};
  words.forEach(word => {
    wordCount[word] = (wordCount[word] || 0) + 1;
  });
  
  return Object.keys(wordCount)
    .filter(word => wordCount[word] > 1)
    .slice(0, 10);
}

// 生成建议
function generateSuggestions(similarity: number, duplicateCount: number, similarities: any[]): string[] {
  const suggestions: string[] = [];
  
  if (similarity < 30) {
    suggestions.push('✅ 文章原创性良好');
    suggestions.push('建议：继续保持原创内容的创作');
  } else if (similarity < 50) {
    suggestions.push('⚠️ 文章存在一定相似性');
    suggestions.push('建议：检查是否有未标注的引用内容');
    suggestions.push('建议：增加更多原创观点和分析');
  } else if (similarity < 70) {
    suggestions.push('⚠️ 文章相似度较高');
    suggestions.push('建议：大幅修改内容结构和表达方式');
    suggestions.push('建议：添加原创的案例和观点');
  } else {
    suggestions.push('❌ 文章相似度过高，存在抄袭风险');
    suggestions.push('建议：重新创作或标注引用来源');
    suggestions.push('建议：确保内容的原创性');
  }
  
  if (duplicateCount > 0) {
    suggestions.push(`发现 ${duplicateCount} 篇高度相似的文章`);
  }
  
  return suggestions;
}
