import { NextRequest, NextResponse } from 'next/server';
import { autoFormat, formatStyles } from '@/lib/formatters';

export async function POST(request: NextRequest) {
  try {
    const { content, styleId = 'classic' } = await request.json();
    
    if (!content) {
      return NextResponse.json({ error: '请提供要格式化的内容' }, { status: 400 });
    }

    // 验证样式ID是否存在
    const validStyleIds = formatStyles.map(style => style.id);
    if (!validStyleIds.includes(styleId)) {
      return NextResponse.json({ 
        error: `无效的样式ID。可用样式: ${validStyleIds.join(', ')}` 
      }, { status: 400 });
    }

    // 应用自动排版
    const formattedContent = autoFormat(content, styleId);

    return NextResponse.json({
      formattedContent,
      styleId,
      styleName: formatStyles.find(s => s.id === styleId)?.name
    });
  } catch (error) {
    console.error('格式化失败:', error);
    return NextResponse.json(
      { error: '格式化失败，请检查内容格式' }, 
      { status: 500 }
    );
  }
}

// 获取可用的格式化样式
export async function GET() {
  return NextResponse.json({
    styles: formatStyles.map(style => ({
      id: style.id,
      name: style.name,
      description: style.description
    }))
  });
}
